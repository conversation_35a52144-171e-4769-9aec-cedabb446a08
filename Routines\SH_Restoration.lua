function A_264(...)
    -- Addon
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    
    -- HeroRotation
    local AoEON = M.AoEON
    local Cast = M.Cast
    local CastMagic = M.CastMagic
    local CastCycleAlly = M.CastCycleAlly
    local CastTargetIfAlly = M.CastTargetIfAlly
    local CastCycle = M.CastCycle
    local CastAlly = M.CastAlly
    
    -- LUA Functions
    local GetWeaponEnchantInfo = _G['GetWeaponEnchantInfo']  -- Get weapon enchant details
    local GetTime = _G['GetTime']  -- Get current time
    local IsInGroup = _G['IsInGroup']  -- Check if player is in a group
    local GetNumGroupMembers = _G['GetNumGroupMembers']  -- Get number of group members
    local CombatLogGetCurrentEventInfo = _G['CombatLogGetCurrentEventInfo']  -- Get combat log info
    local tContains = _G['tContains']  -- Check if table contains value
    local wipe = _G['wipe']  -- Clear table
    local C_PaperDollInfo = _G['C_PaperDollInfo']  -- Character info API
    
    ---@class Shaman
    local Shaman = MainAddon.Shaman

    -- Spells and Items
    local S = Spell.Shaman.Restoration
    local I = Item.Shaman.Restoration

    -- Items excluded from on-use logic
    local OnUseExcludes = {
    }
    
    -- Toggle Setting for Force DPS mode
    MainAddon.Toggle.Special["ForceDPS"] = {
        Icon = MainAddon.GetTexture(S.LavaBurst),
        Name = "Force DPS",
        Description = "This toggle will force DPS rotation.",
        Spec = 264,  -- Restoration spec ID
    }

    -- GUI Configuration Setup
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '0070DD'  -- Shaman blue color
    local Config_Table = {
        key = Config_Key,
        title = 'Shaman - Restoration',
        subtitle = '?? x yuno - ' .. MainAddon.Version,
        width = 650,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            
            -- Single Target Healing Section
            { type = 'header', text = 'Single Target Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Riptide Configuration
            { type = 'header', text = 'Riptide', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'RiptideHP', icon = S.Riptide:ID(), min = 1, max = 100, default = 90 },
            { type = 'spacer' },
            
            -- Primordial Wave Configuration
            { type = 'header', text = 'Primordial Wave', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'PwaveHP', icon = S.PrimordialWaveResto:ID(), min = 1, max = 100, default = 90 },
            { type = 'spacer' },
            
            -- Healing Wave Configuration
            { type = 'header', text = 'Healing Wave', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'HWHP', icon = S.HealingWave:ID(), min = 1, max = 100, default = 90 },
            { type = 'spacer' },
            
            -- Healing Surge Configuration
            { type = 'header', text = 'Healing Surge', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'HSHP', icon = S.HealingSurge:ID(), min = 1, max = 100, default = 45 },
            { type = 'spinner', text = 'Master of the Elements Threshold (%)', key = 'HSHP_MotE', icon = S.HealingSurge:ID(), min = 1, max = 100, default = 80 },
            { type = 'header', text = 'Tidal Waves', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Single Target Threshold (%)', key = 'TWAVEST', icon = S.TidalWaves:ID(), min = 1, max = 100, default = 70 },
            { type = 'spacer' },

            -- Nature's Swiftness Configuration
            { type = 'header', text = "Nature's Swiftness", size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'NS', icon = S.NaturesSwiftness:ID(), min = 1, max = 100, default = 30 },
            { type = 'spacer' },
            
            -- Unleash Life Configuration
            { type = 'header', text = 'Unleash Life', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'UnleashLHP', icon = S.UnleashLife:ID(), min = 1, max = 100, default = 90 },
            { type = 'spacer' },
            
            -- Ancestral Swiftness Configuration
            { type = 'header', text = 'Ancestral Swiftness', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'AnSw', icon = S.AncestralSwiftness:ID(), min = 1, max = 100, default = 80 },
            { type = 'spacer' },
            
            -- Group Healing Section
            { type = 'header', text = 'Group Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Chain Heal Configuration
            { type = 'header', text = 'Chain Heal', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'CH_underX', icon = S.ChainHeal:ID(), min = 1, max = 100, default = 30},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'CH_underX_val', icon = S.ChainHeal:ID(), min = 1, max = 100, default = 75},
            { type = 'header', text = 'High Tide', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'CHHT_underX', icon = S.HighTide:ID(), min = 1, max = 100, default = 30},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'CHHT_underX_val', icon = S.HighTide:ID(), min = 1, max = 100, default = 85},
            { type = 'header', text = 'Tidal Waves', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'TWAVEAOE_underX', icon = S.TidalWaves:ID(), min = 1, max = 100, default = 50},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'TWAVEAOE_underX_val', icon = S.TidalWaves:ID(), min = 1, max = 100, default = 65},
            { type = 'spacer' },
            
            -- Healing Stream/Cloudburst Totem Configuration
            { type = 'header', text = 'Healing Stream/Cloudburst Totem', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'HSTCB1st_underX', icon = 324483, min = 1, max = 100, default = 35}, 
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'HSTCB1st_underX_val', icon = 324483, min = 1, max = 100, default = 85},
            { type = 'checkbox', text = 'High Priority', icon = S.CloudburstTotem:ID(), key = 'highprio_totem', default = true },
            { type = 'checkbox', text = 'Overlap on Emergency', icon = S.HealingStreamTotem:ID(), key = 'overlap_totem', default = true },
            { type = 'header', text = 'Healing Stream Totem (2nd Charge)', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'HSTCB2nd_underX', icon = 324483, min = 1, max = 100, default = 35}, 
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'HSTCB2nd_underX_val', icon = 324483, min = 1, max = 100, default = 65},
            { type = 'spacer' },
            
            -- Recall Cloudburst Totem Configuration
            { type = 'header', text = 'Recall Cloudburst Totem', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'RCB_underX', icon = 307393, min = 1, max = 100, default = 30},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'RCB_underX_val', icon = 307393, min = 1, max = 100, default = 50},
            { type = 'spinner', text = 'Healing Collected Threshold (x5000)', key = 'RCB_Collected_Val', icon = 307393, min = 1, max = 100, default = 50 },
            { type = 'spacer' },
            
            -- Healing Tide Totem Configuration
            { type = 'header', text = 'Healing Tide Totem', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'HTT_underX', icon = S.HealingTideTotem:ID(), min = 1, max = 100, default = 45},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'HTT_underX_val', icon = S.HealingTideTotem:ID(), min = 1, max = 100, default = 55},
            { type = 'spacer' },
            
            -- Healing Rain/Surging Totem Configuration
            { type = 'header', text = 'Healing Rain/Surging Totem', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'HR_underX', icon = S.HealingRain:ID(), min = 1, max = 100, default = 45},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'HR_underX_val', icon = S.HealingRain:ID(), min = 1, max = 100, default = 95},
            { type = 'checkbox', text = 'Magic Groundspell - Healing Rain', icon = S.HealingRain:ID(), key = 'magicgroundspell_healingrain', default = false },
            { type = 'checkbox', text = 'Magic Groundspell - Surging Totem', icon = S.SurgingTotem:ID(), key = 'magicgroundspell_surgingtotem', default = false },
            { type = 'header', text = 'Downpour', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'pour_underX', icon = S.Downpour:ID(), min = 1, max = 100, default = 30},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'pour_underX_val', icon = S.Downpour:ID(), min = 1, max = 100, default = 90},
            { type = 'spacer' },

            -- Ancestral Guidance Configuration
            { type = 'header', text = 'Ancestral Guidance', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'AG_underX', icon = S.AncestralGuidance:ID(), min = 1, max = 100, default = 45},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'AG_underX_val', icon = S.AncestralGuidance:ID(), min = 1, max = 100, default = 70},
            { type = 'spacer' },

            -- Ascendance Configuration
            { type = 'header', text = 'Ascendance', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'A_underX', icon = S.Ascendance:ID(), min = 1, max = 100, default = 45},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'A_underX_val', icon = S.Ascendance:ID(), min = 1, max = 100, default = 70},
            { type = 'spacer' },
            
            -- Wellspring Configuration
            { type = 'header', text = 'Wellspring', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'wspring_underX', icon = S.Wellspring:ID(), min = 1, max = 100, default = 20},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'wspring_underX_val', icon = S.Wellspring:ID(), min = 1, max = 100, default = 85},
            { type = 'spacer' },
            
            -- Totem Management Section
            { type = 'header', text = 'Totem Management', color = Config_Color },
            { type = 'spacer' },
            
            -- Totemic Recall Configuration
            { type = 'header', text = 'Totemic Recall', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Use On These Totems:', key = 'TotemicRecall', icon = S.TotemicRecall:ID(), multiselect = true, 
                list = { 
                    { text = 'Healing Stream Totem', key = 'recallHST' }, 
                    { text = 'Surging Totem', key = 'recallST' }, 
                    { text = 'Earthen Wall Totem', key = 'recallEWT' } 
                }, 
                default = { 'recallHST' } 
            },
            { type = 'spacer' },
            
            -- Totemic Projection Configuration
            { type = 'header', text = 'Totemic Projection', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = 'Magic Groundspell Targeting', icon = S.TotemicProjection:ID(), key = 'magicgroundspell_totemicprojection', default = false },
            { type = 'spacer' },
            
            -- Mana Tide Totem Configuration
            { type = 'header', text = 'Mana Tide Totem', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Mana Threshold (%)', key = 'MTT', icon = S.ManaTideTotem:ID(), min = 1, max = 100, default = 50 },
            { type = 'spacer' },
            
            -- Totem Placement Configuration
            { type = 'header', text = 'Totem Placement', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Stand Still Threshold (seconds)', key = 'TotemMovingValue', icon = S.CloudburstTotem:ID(), min = 0, max = 10, default = 1 },
            { type = 'spacer' },
            
            -- Undulation Configuration
            { type = 'header', text = 'Undulation', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Preferred Spell', icon = S.Undulation:ID(), key = 'undulation',
                list = {
                    { text = 'Healing Surge', key = 1 },
                    { text = 'Healing Wave', key = 2 },
                    { text = 'Auto', key = 3 },
                },
                default = 1,
            },
            { type = 'spacer' },
            
            -- Defensives Section
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'spacer' },
            
            -- Astral Shift Configuration
            { type = 'header', text = 'Astral Shift', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Health Threshold (%)', icon = 108271, key = 'AstralShift', min = 1, max = 100, default_spin = 30, default_check = false },
            { type = 'spacer' },
            
            -- Stone Bulwark Totem Configuration
            { type = 'header', text = 'Stone Bulwark Totem', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Health Threshold (%)', icon = S.StoneBulwarkTotem:ID(), key = 'Bulwark', min = 1, max = 99, default_spin = 25, default_check = true },
            { type = 'spacer' },
            
            -- Earthen Wall Totem Configuration
            { type = 'header', text = 'Earthen Wall Totem', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'EW_underX', icon = S.EarthenWallTotem:ID(), min = 1, max = 100, default = 40},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'EW_underX_val', icon = S.EarthenWallTotem:ID(), min = 1, max = 100, default = 75},
            { type = 'spacer' },
            
            -- Earth Elemental Configuration
            { type = 'header', text = 'Earth Elemental', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Use When:', key = 'EarthElemental', icon = S.EarthElemental:ID(), multiselect = true, 
                list = { 
                    { text = 'On Aggro', key = 'EEDEF_aggro' }, 
                    { text = 'Tank is Dead', key = 'EEDEF_tank_dead' } 
                }, 
                default = { 'EEDEF_aggro', 'EEDEF_tank_dead' } 
            },
            { type = 'spacer' },
            
            -- Movement & Utility Section
            { type = 'header', text = 'Movement & Utility', color = Config_Color },
            { type = 'spacer' },
            
            -- Ghost Wolf Configuration
            { type = 'header', text = 'Ghost Wolf', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Out of Combat Usage', icon = 2645, key = 'AutoWolfOOC', 
                list = {
                    { text = 'Everywhere', key = 1 }, 
                    { text = 'Dungeon', key = 2 }, 
                    { text = 'Raid', key = 3 }, 
                    { text = 'Open World Only', key = 5 }, 
                    { text = 'None', key = 4 }
                }, 
                default = 5 
            },
            { type = 'dropdown', text = 'In Combat Usage', icon = 2645, key = 'AutoWolfCombat', 
                list = {
                    { text = 'Everywhere', key = 1 }, 
                    { text = 'Dungeon', key = 2 }, 
                    { text = 'Raid', key = 3 }, 
                    { text = 'Open World Only', key = 5 }, 
                    { text = 'None', key = 4 }
                }, 
                default = 4 
            },            
            { type = 'spinner', text = 'Movement Threshold (seconds)', icon = S.GhostWolf:ID(), key = 'wolf', min = 0, max = 15, default_spin = 1.5 },
            { type = 'checkbox', text = 'Stop Rotation When Active', icon = S.GhostWolf:ID(), key = 'StopWolf', default = false },
            { type = 'spacer' },
            
            -- Spiritwalker's Grace Configuration
            { type = 'header', text = "Spiritwalker's Grace", size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Movement Threshold (seconds)', icon = S.SpiritwalkersGrace:ID(), key = 'SWG', min = 0, max = 15, default_spin = 2 },
            { type = 'spinner', text = "Group Health Threshold (%)", icon = S.SpiritwalkersGrace:ID(), key = 'SWG_HP', min = 0, max = 100, default_spin = 75 },
            { type = 'spacer' },
            
            -- Skyfury Totem Configuration
            { type = 'header', text = 'Skyfury Totem', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Use For:', key = 'sky', icon = S.Skyfury:ID(), multiselect = true,
                list = {
                    { text = 'Self', key = 'sky_self' },
                    { text = 'Friends', key = 'sky_friends' },
                },
                default = {
                    'sky_self',
                    'sky_friends'
                },
            },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'ruler' },
            { type = 'spacer' },
        }
    }
    
    -- Add additional configuration sections
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildHealingTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatManaPotionUI(Config_Table.config, "Restoration", Config_Color)
    M.SetConfig(264, Config_Table)

    -- Variables and State Management
    local Tanks, Healers, Members, Damagers, Melees, TargetIfAlly
    local Enemies40y = {}
    local Enemies10ySplash = {}
    local EnemiesCount10ySplash = 0
    local Settings = {}
    local Var = {}
    
    -- Initialize state variables
    Var['CombatMonitor_TimeStamp'] = GetTime()
    Var['CombatMonitor_State'] = false
    Var['MembersGUID'] = {}
    Var['HealingRaidTable'] = {}
    Var['HealingRaidTableTimeSince'] = {}
    Var['UndulationCounter'] = 1
    Var['UndulationCounterP'] = 0
    Var['IsStandingStillFor'] = 999
    Settings['TotemMovingValue'] = 1

    -- Environment state tracking
    local inDungeon, inRaid, inCombat, isMoving
    
    ---@param Totem Spell
    ---@param ReturnTime boolean?
    -- Find active totem and optionally return remaining time
    local function TotemFinder(Totem, ReturnTime)
        for i = 1, 6, 1 do
          local TotemActive, TotemName, StartTime, Duration = Player:GetTotemInfo(i)
          if Totem:Name() == TotemName then
            if ReturnTime then
              return math.max(Duration - (GetTime() - StartTime), 0)
            else
              return true
            end
          end
        end
    end

    -- Monitor combat state based on tank affection
    local function combatMonitor()
        -- Return cached state if check was done recently
        if GetTime() - Var['CombatMonitor_TimeStamp'] < 1 then
            return Var['CombatMonitor_State']
        end
        
        if Tanks then
            ---@param TankUnit Unit
            for _, TankUnit in pairs(Tanks) do
                if TankUnit:AffectingCombat() then
                    Var['CombatMonitor_TimeStamp'] = GetTime()
                    Var['CombatMonitor_State'] = true
                    return true
                end
            end
        end

        Var['CombatMonitor_TimeStamp'] = GetTime()
        Var['CombatMonitor_State'] = false
        return false
    end

    -- Get and store GUIDs for all raid/party members
    local function GetMembersGUID()
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
        
        if not Var['MembersGUID'] then
            Var['MembersGUID'] = {}
        else
            wipe(Var['MembersGUID'])
        end

        -- Process each member and store their GUID for lookup
        if Members and #Members > 0 then
            for i = 1, #Members do
                ---@type Unit
                local member = Members[i]
                local guid = member:GUID()

                if guid then
                    Var['MembersGUID'][guid] = member
                end
            end
        end
    end

    -- Update variables and settings each frame
    local function UpdateVars()
        -- Refresh group composition
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
        
        -- Party size calculations
        Var['partySize'] = GetNumGroupMembers()
        Var['MembersAmount'] = Var['partySize'] < 5 and 5 or Var['partySize']
        Var['IsStandingStillFor'] = Player:IsStandingStillFor()

        -- Calculate thresholds based on party size for AoE healing
        if Var['MembersAmount'] then
            Settings['CH_underX'] = (GetSetting('CH_underX', 50) * Var['MembersAmount']) / 100
            Settings['CH_underX_val'] = GetSetting('CH_underX_val', 50)
            Settings['CHHT_underX'] = (GetSetting('CHHT_underX', 50) * Var['MembersAmount']) / 100
            Settings['CHHT_underX_val'] = GetSetting('CHHT_underX_val', 50)
            Settings['HSTCB1st_underX'] = (GetSetting('HSTCB1st_underX', 35) * Var['MembersAmount']) / 100
            Settings['HSTCB1st_underX_val'] = GetSetting('HSTCB1st_underX_val', 85)
            Settings['HSTCB2nd_underX'] = (GetSetting('HSTCB2nd_underX', 35) * Var['MembersAmount']) / 100
            Settings['HSTCB2nd_underX_val'] = GetSetting('HSTCB2nd_underX_val', 65)
            Settings['RCB_underX'] = (GetSetting('RCB_underX', 50) * Var['MembersAmount']) / 100
            Settings['RCB_underX_val'] = GetSetting('RCB_underX_val', 50)
            Settings['HTT_underX'] = (GetSetting('HTT_underX', 50) * Var['MembersAmount']) / 100
            Settings['HTT_underX_val'] = GetSetting('HTT_underX_val', 50)
            Settings['HR_underX'] = (GetSetting('HR_underX', 50) * Var['MembersAmount']) / 100
            Settings['HR_underX_val'] = GetSetting('HR_underX_val', 50)
            Settings['pour_underX'] = (GetSetting('pour_underX', 50) * Var['MembersAmount']) / 100
            Settings['pour_underX_val'] = GetSetting('pour_underX_val', 50)
            Settings['AG_underX'] = (GetSetting('AG_underX', 50) * Var['MembersAmount']) / 100
            Settings['AG_underX_val'] = GetSetting('AG_underX_val', 50)
            Settings['A_underX'] = (GetSetting('A_underX', 50) * Var['MembersAmount']) / 100
            Settings['A_underX_val'] = GetSetting('A_underX_val', 50)
            Settings['EW_underX'] = (GetSetting('EW_underX', 50) * Var['MembersAmount']) / 100
            Settings['EW_underX_val'] = GetSetting('EW_underX_val', 50)
            Settings['TWAVEAOE_underX'] = (GetSetting('TWAVEAOE_underX', 50) * Var['MembersAmount']) / 100
            Settings['TWAVEAOE_underX_val'] = GetSetting('TWAVEAOE_underX_val', 65)
            Settings['wspring_underX'] = (GetSetting('wspring_underX', 20) * Var['MembersAmount']) / 100
            Settings['wspring_underX_val'] = GetSetting('wspring_underX_val', 85)
        end

        -- Update single-target healing thresholds
        Settings['RiptideHP'] = GetSetting('RiptideHP', 90)
        Settings['PwaveHP'] = GetSetting('PwaveHP', 90)
        Settings['HWHP'] = GetSetting('HWHP', 90)
        Settings['HSHP'] = GetSetting('HSHP', 45)
        Settings['HSHP_MotE'] = GetSetting('HSHP_MotE', 80)
        Settings['NS'] = GetSetting('NS', 30)
        Settings['UnleashLHP'] = GetSetting('UnleashLHP', 90)
        Settings['AnSw'] = GetSetting('AnSw', 80)
        Settings['TWAVEST'] = GetSetting('TWAVEST', 85)

        -- Miscellaneous settings
        Settings['MTT'] = GetSetting('MTT', 50)
        Settings['RCB_Collected_Val'] = GetSetting('RCB_Collected_Val', 50) * 5000
        Settings['undulation'] = GetSetting('undulation', 1)
        Settings['highprio_totem'] = GetSetting('highprio_totem', true)
        Settings['TotemMovingValue'] = GetSetting('TotemMovingValue', 1)
        Settings['overlap_totem'] = GetSetting('overlap_totem', true)

        -- Utility settings
        Settings['wolf'] = GetSetting('wolf', 1.5)
        Settings['StopWolf'] = GetSetting('StopWolf', false)
        Settings['SWG'] = GetSetting('SWG', 2)
        Settings['SWG_HP'] = GetSetting('SWG_HP', 75)
        Settings['AutoWolfCombat'] = GetSetting('AutoWolfCombat', 4)
        Settings['AutoWolfOOC'] = GetSetting('AutoWolfOOC', 1)
        Settings['magicgroundspell_healingrain'] = GetSetting('magicgroundspell_healingrain', false)
        Settings['magicgroundspell_totemicprojection'] = GetSetting('magicgroundspell_totemicprojection', false)
        Settings['magicgroundspell_surgingtotem'] = GetSetting('magicgroundspell_surgingtotem', false)
        
        -- Update dynamic state variables
        Var['AverageHPInRange'] = HealingEngine:MedianHP(true)
        Var['TargetIsValid'] = M.TargetIsValid()
        Var['IsInCombat'] = Player:AffectingCombat()
        Var['ManaPct'] = Player:ManaPercentage()
        Var['GhostWolfBuff'] = Player:BuffUp(S.GhostWolf)
        Var['SpiritwalkersGrace'] = Player:BuffUp(S.SpiritwalkersGrace)
        Var['IsMovingFor'] = Player:IsMovingFor()
        Var['IsInDungeon'] = Player:IsInDungeonArea()
        Var['IsInRaid'] = Player:IsInRaidArea()
        Var['HasMainHandEnchant'], Var['MHEnchantTimeRemains'], _, _, Var['hasOffHandEnchant'], Var['offHandExpiration'] = GetWeaponEnchantInfo()

        -- Check for combat via tanks if player isn't in combat
        if not Var['IsInCombat'] then
            Var['IsInCombat'] = combatMonitor()
        end

        -- Update member GUIDs if needed
        if HL.Utils.tableCount(Var['MembersGUID']) == 0 and Members and #Members > HL.Utils.tableCount(Var['MembersGUID']) then
            GetMembersGUID()
        end

        -- Reset healing rain tracking when abilities are ready again
        if #Var['HealingRaidTable'] > 0 and S.HealingRain:CooldownRemains(nil, true) == 0 and S.SurgingTotem:CooldownRemains(nil, true) == 0 then
            Var['HealingRaidTable'] = {}
            Var['HealingRaidTableTimeSince'] = {}
        end

        -- Update Undulation counter
        Var['UndulationCounterP'] = Player:UndulationCounterP() or 0
    end

    -- Get amount of healing stored in Cloudburst Totem
    local function CloudBurstAmountStored()
        if Player:BuffUp(S.CloudburstTotemBuff) then
            local Points = Player:AuraInfo(S.CloudburstTotemBuff, nil, true).points
            local CBAmount = Points and Points[1] or 0
            return CBAmount
        end
        return 0
    end

    -- Count how many non-player Earth Shields are active
    local function CountEarthShield()
        local count = 0
        ---@param ThisUnit Unit
        for _, ThisUnit in pairs(Members) do
            if (ThisUnit:BuffUp(S.EarthShieldSelfBuff) or ThisUnit:BuffUp(S.EarthShieldOtherBuff)) and not ThisUnit:IsUnit(Player) then
                count = count + 1
            end
        end
        return count
    end

    -- Count how many Flame Shock debuffs are active
    local function CountFlameShock()
        local count = 0
        ---@param ThisUnit Unit
        for _, ThisUnit in pairs(Enemies40y) do
            if ThisUnit:DebuffUp(S.FlameShock) then
                count = count + 1
            end
        end
        return count
    end

    -- Evaluation functions for target selection
    
    -- Always return true for unconditional casting
    local function EvaluateTrue()
        return true
    end
    
    ---@param TargetUnit Unit
    -- Check if unit's health is below Chain Heal High Tide threshold
    local function EvaluateChainHeal(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['CHHT_underX_val']
    end
    
    ---@param TargetUnit Unit
    -- Check if unit's health is suitable for buffed Chain Heal
    local function EvaluateChainHealBuffed(TargetUnit)
        return TargetUnit:HealthPercentage() <= 90
    end
    
    ---@param TargetUnit Unit
    -- Check if unit is below Tidal Waves AoE threshold
    local function EvaluateChainHealTidalWaves(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['TWAVEAOE_underX_val']
    end
    
    ---@param TargetUnit Unit
    -- Return unit's health percentage for sorting
    local function EvaluateTargetIfHP(TargetUnit)
        return TargetUnit:HealthPercentage()
    end
    
    ---@param TargetUnit Unit
    -- Check if Flame Shock should be refreshed on target
    local function EvaluateCycleFlameShock(TargetUnit)
        return TargetUnit:DebuffRefreshable(S.FlameShock) and TargetUnit:TimeToDie() > 12
    end
    
    ---@param TargetUnit Unit
    -- Check if unit needs Riptide based on configuration
    local function EvaluateRiptide(TargetUnit)
        return TargetUnit:BuffRefreshable(S.Riptide) and TargetUnit:HealthPercentage() <= Settings['RiptideHP']
    end
    
    ---@param TargetUnit Unit
    -- Check if unit is suitable for Primordial Wave
    local function EvaluatePrimordialWave(TargetUnit)
        return TargetUnit:BuffRefreshable(S.Riptide) and TargetUnit:HealthPercentage() <= Settings['PwaveHP']
    end
    
    ---@param TargetUnit Unit
    -- Check if Riptide needs to be refreshed on unit
    local function EvaluateRiptideUptime(TargetUnit)
        return TargetUnit:BuffRefreshable(S.Riptide)
    end
    
    ---@param TargetUnit Unit
    -- Check if unit is below Tidal Waves threshold
    local function EvaluateTidalWaves(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['TWAVEST']
    end
    
    ---@param TargetUnit Unit
    -- Check if Riptide should be maintained for Undercurrent
    local function EvaluateRiptideUndercurrent(TargetUnit)
        return TargetUnit:BuffRefreshable(S.Riptide)
    end
    
    ---@param TargetUnit Unit
    -- Check if unit needs Healing Surge based on threshold
    local function EvaluateHealingSurge(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['HSHP']
    end
    
    ---@param TargetUnit Unit
    -- Check if unit is suitable for Master of the Elements Healing Surge
    local function EvaluateHealingSurgeMasterOfTheElements(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['HSHP_MotE']
    end
    
    ---@param TargetUnit Unit
    -- Check if unit needs Healing Wave based on threshold
    local function EvaluateHealingWave(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['HWHP']
    end
    
    ---@param TargetUnit Unit
    -- Fallback evaluation for Healing Wave
    local function EvaluateHealingWaveFallBack(TargetUnit)
        return TargetUnit:HealthPercentage() <= 95
    end
    
    ---@param TargetUnit Unit
    -- Check if unit needs Earth Shield
    local function EvaluateEarthShield(TargetUnit)
        return TargetUnit:BuffDown(S.EarthShieldSelfBuff, true) and TargetUnit:BuffDown(S.EarthShieldOtherBuff, true)
    end
    
    ---@param TargetUnit Unit
    -- Check if player needs Earth Shield for Elemental Orbit
    local function EvaluateEarthShieldSelf(TargetUnit)
        return TargetUnit:BuffDown(S.EarthShieldSelfBuff, true) and TargetUnit:BuffDown(S.EarthShieldOtherBuff, true) and TargetUnit:IsUnit(Player)
    end
    
    ---@param TargetUnit Unit
    -- Check if unit needs Unleash Life
    local function EvaluateUnleashLife(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['UnleashLHP']
    end

    -- Handle Ghost Wolf form based on settings
    local function AutoWolf()
        -- In combat Ghost Wolf
        if inCombat and GetSetting('AutoWolfCombat', 4) ~= 4 and Player:BuffDown(S.GhostWolf) then
            if isMoving and S.GhostWolf:IsReady(Player) and not Target:IsInRange(30) then
                if GetSetting('AutoWolfCombat', 4) == 1 then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf Combat 1"
                    end
                end
                if GetSetting('AutoWolfCombat', 4) == 2 and inDungeon then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf Combat 2"
                    end
                end
                if GetSetting('AutoWolfCombat', 4) == 3 and inRaid then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf Combat 3"
                    end
                end
                if GetSetting('AutoWolfOOC', 4) == 5 and not inDungeon and not inRaid then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 5"
                    end
                end
            end
        end

        -- Out of combat Ghost Wolf
        if not inCombat and GetSetting('AutoWolfOOC', 5) ~= 4 and Player:BuffDown(S.GhostWolf) then
            if Player:IsMovingFor() >= 2 and S.GhostWolf:IsReady(Player) then
                if GetSetting('AutoWolfOOC', 5) == 1 then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 1"
                    end
                end
                if GetSetting('AutoWolfOOC', 5) == 2 and inDungeon then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 2"
                    end
                end
                if GetSetting('AutoWolfOOC', 5) == 3 and inRaid then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 3"
                    end
                end
                if GetSetting('AutoWolfOOC', 5) == 5 and not inDungeon and not inRaid then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 5"
                    end
                end
            end
        end        
    end

    -- Core utility spells that should be maintained
    local function Utilities()
        -- Water Shield
        if S.WaterShield:IsReady(Player) and Player:BuffDown(S.WaterShield) and M.Timers:AddDelay("WaterShield", 2, 0.5) then
            if Cast(S.WaterShield) then return 'Water Shield' end
        end

        -- Earth Shield management
        if S.EarthShield:IsReady(Player) then
            CountEarthShield()
            -- Apply to tanks first if none are active
            if CountEarthShield() == 0 then
                if CastCycleAlly(S.EarthShield, Tanks, EvaluateEarthShield) then
                    return "Defensive: Earth Shield - Tanks";
                end
            end
            -- Apply to self if Elemental Orbit is talented
            if S.ElementalOrbit:IsAvailable() then
                if CastCycleAlly(S.EarthShield, Healers, EvaluateEarthShieldSelf) then
                    return "Defensive: Earth Shield - Player";
                end
            end
        end

        -- Skyfury Totem management
        local sky = GetSetting('sky', {})
        if S.Skyfury:IsReady(Player) and (sky['sky_self'] and Player:BuffDown(S.Skyfury, true) or sky['sky_friends'] and M.GroupBuffMissing(S.Skyfury)) then
            if Cast(S.Skyfury) then
                return "Skyfury";
            end
        end

        -- Tidecaller's Guard weapon enchant
        if C_PaperDollInfo.OffhandHasShield() and S.TidecallersGuard:IsReady(Player) then
            if (not Var['hasOffHandEnchant'] or Var['offHandExpiration'] < 600000) then
                if Cast(S.TidecallersGuard) then
                    return "Tidecaller's Guard"
                end
            end
        end
    end

    -- Additional utility spells that can be used when no healing is needed
    local function UtilitiesFiller()
        -- Spiritwalker's Grace for movement during high healing need
        if Var['IsInCombat'] and S.SpiritwalkersGrace:IsReady(Player) and Var['IsMovingFor'] > Settings['SWG'] and Var['AverageHPInRange'] <= Settings['SWG_HP'] then
            if Cast(S.SpiritwalkersGrace, true) then return 'Spiritwalkers Grace' end
        end

        -- Maintain Earthliving Weapon enchant
        if (not Var['HasMainHandEnchant'] or Var['MHEnchantTimeRemains'] < 300 and not Var['IsInCombat']) and S.EarthlivingWeapon:IsReady(Player) then
            if Cast(S.EarthlivingWeapon) then return 'Earthliving Weapon' end
        end
    end

    -- Defensive cooldowns
    local function Defensives()
        local PlayerHealthPercentage = Player:RealHealthPercentage()
        
        -- Astral Shift
        if GetSetting('AstralShift_check', false) and S.AstralShift:IsReady(Player) and PlayerHealthPercentage <= GetSetting('AstralShift_spin', 30) then
            if Cast(S.AstralShift, true) then
                return "Defensive: Astral Shift";
            end
        end

        -- Stone Bulwark Totem
        if GetSetting('Bulwark_check', false) and S.StoneBulwarkTotem:IsReady(Player) and PlayerHealthPercentage <= GetSetting('Bulwark_spin', 30) then
            if Cast(S.StoneBulwarkTotem) then
                return "Defensive: Buwlark";
            end
        end
        
        -- Earth Elemental - for emergency tanking
        if Var['IsInCombat'] and S.EarthElemental:IsReady(Player) then
            local EEUsage = GetSetting('EarthElemental', {})
            if IsInGroup() and Var['IsInDungeon'] then
                -- Use when player has aggro
                if EEUsage['EEDEF_aggro'] and Player:IsTankingAoE(40) then
                    if Cast(S.EarthElemental) then
                        return "Defensive: Earth Elemental - Aggro"
                    end
                end
                -- Use when tank is dead
                if EEUsage['EEDEF_tank_dead'] then
                    local TankDead = false
                    if Tanks then
                        for _, TankUnit in pairs(Tanks) do
                            if TankUnit:IsDeadOrGhost() then
                                TankDead = true
                                break
                            end
                        end
                    end
                    if TankDead then
                        if Cast(S.EarthElemental) then
                            return "Defensive: Earth Elemental - Tank Dead";
                        end
                    end
                end
            end
        end
    end

    -- On-Use items and trinkets
    local function Items()
        -- Iridal the Earth's Master
        if I.IridaltheEarthsMaster:IsEquippedAndReady() and Target:HealthPercentage() < 35 then
             if Cast(I.IridaltheEarthsMaster) then return "iridal_the_earths_master"; end
        end

        -- Dreambinder
        if I.Dreambinder:IsEquippedAndReady() then
            if MainAddon.SetTopTexture(1, "Weapon On-Use") then return "dreambinder_loom_of_the_great_cycle"; end
        end
   end

    -- Special healing cases (e.g., for focus target)
    local function HealingSpecial()
        -- Check if we should heal target or mouseover
        local shouldHeal, isReadyToBeHealed, type = HealingEngine.ShouldHealTargetOrMouseover()
        if shouldHeal and isReadyToBeHealed then
            if Focus:IsInRange(40) then
                -- Riptide
                if S.Riptide:IsReady(Focus) and Focus:BuffDown(S.Riptide) and Player:BuffDown(S.TidalWaves) then
                    -- Unleash Life to boost Riptide
                    if S.UnleashLife:IsReady(Focus) then
                        if CastAlly(S.UnleashLife, Focus) then
                            return "Special Healing: Unleash Life"
                        end
                    end
                    if CastAlly(S.Riptide, Focus) then
                        return "Special Healing: Riptide"
                    end
                end
                -- Healing Rain for focus area
                if S.HealingRain:IsReady(Focus) then
                    if CastMagic(S.HealingRain, nil, "73920-Magic", Settings['magicgroundspell_healingrain']) then
                        return "Special Healing: Healing Rain"
                    end 
                end
                -- Healing Surge for quick heals
                if S.HealingSurge:IsReady(Focus) then
                    if CastAlly(S.HealingSurge, Focus) then
                        return "Special Healing: Healing Surge"
                    end
                end
            end
        elseif not isReadyToBeHealed then
            -- Indicate target needs to be set
            if type == "MouseOver" then
                MainAddon.SetTopColor(1, "Focus Mouseover")
            elseif type == "Target" then
                MainAddon.SetTopColor(1, "Focus Target")
            end
        end
    end

    -- Prepare for incoming damage by pre-casting healing abilities
    local function DamageIncoming()
        -- Unleash Life to boost next heal
        if S.UnleashLife:IsReady(Player) then
            if CastCycleAlly(S.UnleashLife, Members, EvaluateTrue) then
                return "Damage Incoming: Unleash Life"
            end
        end
        
        -- Stone Bulwark Totem for damage reduction
        if S.StoneBulwarkTotem:IsReady(Player) and Var['AverageHPInRange'] <= 58 then
            if Cast(S.StoneBulwarkTotem) then
                return "Damage Incoming: Stone Bulwark Totem (safety net)"
            end
        end
        
        -- Surging Totem for area healing
        if S.SurgingTotem:IsReady(Player) and not Player:TotemIsActive(Var['SurgingTotemName']) then
            if Cast(S.SurgingTotem) then
                return "Damage Incoming: Surging Totem"
            end
        end
        
        -- Cloudburst Totem to collect healing
        if S.CloudburstTotem:IsReady(Player) and not Player:TotemIsActive(Var['CloudburstTotemName']) then
            if Cast(S.CloudburstTotem) then
                return "Damage Incoming: Cloudburst Totem"
            end
        end
        
        -- Healing Stream Totem for sustained healing
        if S.HealingStreamTotem:IsReady(Player) and not Player:TotemIsActive(Var['HealingStreamTotemName']) then
            if Cast(S.HealingStreamTotem) then
                return "Damage Incoming: Healing Stream Totem"
            end
        end
    end

    -- Healing cooldowns to use out of combat
    local function HealingCDsOOC()
        -- Recall Cloudburst Totem when ready
        if S.RecallCloudburstTotem:IsReady(Player) and HealingEngine:MembersUnderPercentage(Settings['RCB_underX_val']) >= Settings['RCB_underX']  and CloudBurstAmountStored() >= Settings['RCB_Collected_Val'] then
            if Cast(S.RecallCloudburstTotem, true) then
                return "Recall Cloudburst Totem"
            end
        end
    end

    -- Major healing cooldowns for combat
    local function HealingCDsCombat()
        -- Ancestral Guidance
        if S.AncestralGuidance:IsReady(Player) and HealingEngine:MembersUnderPercentage(Settings['AG_underX_val']) >= Settings['AG_underX'] then
            if Cast(S.AncestralGuidance, true) then
                return "Ancestral Guidance"
            end
        end
        
        -- Nature's Swiftness for emergency healing
        if S.NaturesSwiftness:IsReady(Player) and S.HealingSurge:IsReady(Player) and HealingEngine:LowestHP(true, 40) <= Settings['NS'] then
            if Cast(S.NaturesSwiftness, true) then
                return "Nature's Swiftness"
            end
        end
        
        -- Healing Tide Totem for group-wide healing
        if S.HealingTideTotem:IsReady(Player) and HealingEngine:MembersUnderPercentage(Settings['HTT_underX_val']) >= Settings['HTT_underX'] then
            if Cast(S.HealingTideTotem) then
                return "Healing Tide Totem"
            end
        end
        
        -- Mana Tide Totem for mana regeneration
        if S.ManaTideTotem:IsReady(Player) and Var['ManaPct'] <= Settings['MTT'] then
            if Cast(S.ManaTideTotem) then
                return "Mana Tide Totem"
            end
        end
        
        -- Ascendance for critical healing periods
        if S.Ascendance:IsReady(Player) and HealingEngine:MembersUnderPercentage(Settings['A_underX_val']) >= Settings['A_underX'] then
            if Cast(S.Ascendance) then
                return "Ascendance"
            end
        end
    end

    -- Core healing rotation
    local function HealingRotation()
        -- Priority 1: Use instant Healing Surge with Nature's Swiftness
        if S.HealingSurge:IsReady(Player) and Player:BuffUp(S.NaturesSwiftness) then
            if CastTargetIfAlly(S.HealingSurge, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                return "Healing Surge x Nature's Swiftness"
            end
        end

        -- Downpour when already positioned
        if #Var['HealingRaidTable'] > 0 then
            if S.Downpour:IsReady(Player) then 
                if (HealingEngine:MembersUnderPercentage(Settings['pour_underX_val'], Var['HealingRaidTable']) >= Settings['pour_underX']) then
                    if Cast(S.Downpour) then
                        return "Downpour"
                    end
                end
                -- Use Downpour before buff expires
                if (Player:BuffRemains(S.DownpourBuff) <= (Player:GCD() * 3)) then
                    if Cast(S.Downpour) then
                        return "Downpour Fading"
                    end
                end
            end
        end

        -- Healing Stream Totem - intelligent charge management
        if S.HealingStreamTotem:IsReady(Player) then
            local charges = S.HealingStreamTotem:ChargesFractional()
        
            -- High Priority usage when group health is declining
            if Settings['highprio_totem'] and charges >= 1 and Var['AverageHPInRange'] <= 90 and Var['IsInCombat'] then
                -- Check if totem is already active
                if not Player:TotemIsActive(Var['HealingStreamTotemName']) or Settings['overlap_totem'] then
                    if Cast(S.HealingStreamTotem) then
                        return 'Healing Stream Totem - high priority'
                    end
                end
            end
        
            -- Almost capped charges (use first charge)
            if charges >= 1.9 then
                if HealingEngine:MembersUnderPercentage(Settings['HSTCB1st_underX_val']) >= Settings['HSTCB1st_underX'] then
                    if not Player:TotemIsActive(Var['HealingStreamTotemName']) or Settings['overlap_totem'] then
                        if Cast(S.HealingStreamTotem) then
                            return "Healing Stream Totem - First Charge"
                        end
                    end
                end
            -- Second charge management (conserve one charge)
            elseif charges >= 1 and charges < 1.9 then
                if HealingEngine:MembersUnderPercentage(Settings['HSTCB2nd_underX_val']) >= Settings['HSTCB2nd_underX'] then
                    if not Player:TotemIsActive(Var['HealingStreamTotemName']) or Settings['overlap_totem'] then
                        if Cast(S.HealingStreamTotem) then
                            return "Healing Stream Totem - Second Charge"
                        end
                    end
                end
            end
        end             

        -- Emergency overlap of Healing Stream Totem
        if S.HealingStreamTotem:IsReady(Player) and Player:TotemIsActive(Var['HealingStreamTotemName']) and Settings['overlap_totem'] then
            if HealingEngine:MedianHP() <= 62 then
                if Cast(S.HealingStreamTotem) then
                    return "Healing Stream Totem Overlap - Emergency"
                end
            end
        end

        -- Cloudburst Totem management
        if S.CloudburstTotem:IsReady(Player) and not Player:TotemIsActive(Var['CloudburstTotemName']) then
            -- High priority usage
            if Settings['highprio_totem'] and S.CloudburstTotem:ChargesFractional() >= 1.9 and Var['AverageHPInRange'] <= 90 and Var['IsInCombat'] then
                if Cast(S.CloudburstTotem) then
                    return 'Cloudburst Totem - high priority'
                end
            end

            local charges = S.CloudburstTotem:ChargesFractional()
            -- First charge when almost capped
            if charges >= 1.9 then
                if HealingEngine:MembersUnderPercentage(Settings['HSTCB1st_underX_val']) >= Settings['HSTCB1st_underX'] then
                    if Cast(S.CloudburstTotem) then
                        return "Cloudburst Totem - First Charge"
                    end
                end
            -- Second charge management
            elseif charges >= 1 and charges < 1.9 then
                if HealingEngine:MembersUnderPercentage(Settings['HSTCB2nd_underX_val']) >= Settings['HSTCB2nd_underX'] then
                    if Cast(S.CloudburstTotem) then
                        return "Cloudburst Totem - Second Charge"
                    end
                end
            end
        end

        -- Riptide to generate Tidal Waves
        if S.Riptide:IsReady(Player) and Player:BuffDown(S.TidalWaves) then
            -- Prioritize tanks
            if CastCycleAlly(S.Riptide, Tanks, EvaluateRiptide) then
                return "Riptide x Tanks"
            end

            -- Then other raid members
            if CastCycleAlly(S.Riptide, Members, EvaluateRiptide) then
                return "Riptide x Members"
            end
        end

        -- Ancestral Swiftness for faster heals
        if S.AncestralSwiftness:IsAvailable() and S.AncestralSwiftness:IsReady(Player) and HealingEngine:LowestHP(true, 40) <= Settings['AnSw'] then
            if Cast(S.AncestralSwiftness) then
                return "Ancestral Swiftness"
            end
        end  

        -- Leverage Unleash Life buff when active
        if Player:BuffUp(S.UnleashLifeBuff) then
            -- Chain Heal with High Tide and Unleash Life
            if S.ChainHeal:IsReady(Player) and Player:BuffUp(S.HighTideBuff) then
                if CastCycleAlly(S.ChainHeal, Members, EvaluateChainHealBuffed) then
                    return "Unleash Life x Chain Heal"
                end
            end
            
            -- Healing Wave with Primordial Wave and Unleash Life
            if S.HealingWave:IsReady(Player) and Player:BuffUp(S.PrimordialWaveBuff) and Player:BuffRemains(S.UnleashLifeBuff) < 2 then
                if CastTargetIfAlly(S.HealingWave, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then   
                    return "Unleash Life x Healing Wave (buffed by PrimordialWave)"
                end
            end
            
            -- Downpour with Unleash Life for positioned group
            if #Var['HealingRaidTable'] > 0 then
                if S.Downpour:IsReady(Player) then
                    if HealingEngine:MembersUnderPercentage(Settings['pour_underX_val'], Var['HealingRaidTable']) >= Settings['pour_underX'] then
                        if Cast(S.Downpour) then
                            return "Unleash Life x Downpour"
                        end
                    end
                end
            end
        end 

        -- Wellspring for wide-spread healing
        if S.Wellspring:IsReady(Player) and HealingEngine:MembersUnderPercentage(Settings['wspring_underX_val']) >= Settings['wspring_underX'] then
            if Cast(S.Wellspring) then
                return "Wellspring"
            end
        end

        -- Primordial Wave to spread Riptide effect
        if S.PrimordialWaveResto:IsReady(Player) then
            if CastCycleAlly(S.PrimordialWaveResto, Members, EvaluatePrimordialWave) then
                return "Primordial Wave"
            end
        end

        -- Unleash Life to boost next heal
        if S.UnleashLife:IsReady(Player) then
            if CastCycleAlly(S.UnleashLife, Members, EvaluateUnleashLife) then
                return "Unleash Life"
            end
        end

        -- Tidal Waves management
        if Player:BuffUp(S.TidalWaves) then
            -- In organized groups with High Tide, use Chain Heal when appropriate
            if (Var['IsInRaid'] or Var['IsInDungeon']) and Player:BuffUp(S.HighTideBuff) and HealingEngine:MembersUnderPercentage(Settings['TWAVEAOE_underX_val']) >= Settings['TWAVEAOE_underX'] then
                if S.ChainHeal:IsReady(Player) then
                    if CastCycleAlly(S.ChainHeal, Members, EvaluateChainHealTidalWaves) then
                        return "Tidal Waves x Chain Heal (AOE)"
                    end
                end
            -- Raid fallback with High Tide when multiple members low
            elseif Var['IsInRaid'] and Player:BuffUp(S.HighTideBuff) and HealingEngine:MembersUnderPercentage(62) > 5 then
                if S.ChainHeal:IsReady(Player) then
                    if CastTargetIfAlly(S.ChainHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                        return "Tidal Waves fallback (raid)"
                    end
                end
            -- Dungeon fallback with High Tide when several members low
            elseif Var['IsInDungeon'] and Player:BuffUp(S.HighTideBuff) and HealingEngine:MembersUnderPercentage(50) > 3 then
                if S.ChainHeal:IsReady(Player) then
                    if CastTargetIfAlly(S.ChainHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                        return "Tidal Waves fallback (dungeon)"
                    end
                end
            -- Otherwise use Healing Surge for Tidal Waves buff
            else
                if (Var['IsInRaid'] or Var['IsInDungeon']) then
                    if S.HealingSurge:IsReady(Player) then
                        if CastCycleAlly(S.HealingSurge, Members, EvaluateTidalWaves) then
                            return "Tidal Waves x Healing Surge"
                        end
                    end
                end
            end
        end

        -- Chain Heal with High Tide buff
        if S.ChainHeal:IsReady(Player) then
            -- Use when High Tide is about to expire or when many members need healing
            if Player:BuffUp(S.HighTideBuff) and Player:BuffRemains(S.HighTideBuff) < S.ChainHeal:CastTime() * 2 
            or HealingEngine:MembersUnderPercentage(Settings['CHHT_underX_val']) >= Settings['CHHT_underX'] then
                if CastCycleAlly(S.ChainHeal, Members, EvaluateChainHeal) then
                    return "Chain Heal"
                end
            end
        end

        -- Healing Wave with Primordial Wave or Undulation
        if Var['UndulationCounterP'] < 2 or Var['undulation'] == 2 then
            if S.HealingWave:IsReady(Player) and Player:BuffUp(S.PrimordialWaveBuff) and HealingEngine:BuffTotal(S.Riptide) > 0 then
                -- Use Ancestral Swiftness if available
                if S.AncestralSwiftness:IsAvailable() and S.AncestralSwiftness:IsReady(Player) then
                    if Cast(S.AncestralSwiftness) then
                        return "Ancestral Swiftness"
                    end
                end

                -- Cast Healing Wave on appropriate target
                if CastCycleAlly(S.HealingWave, Members, EvaluateHealingWave) then
                    return "Healing Wave x Primordial Wave"
                end

                -- Use before buff expires even if target isn't optimal
                if Player:BuffRemains(S.PrimordialWaveBuff) < S.HealingWave:CastTime() * 1.5 then
                    if CastCycleAlly(S.HealingWave, Members, EvaluateHealingWaveFallBack) then
                        return "Healing Wave x Primordial Wave (Time's up)"
                    end
                end
            end
        end         

        -- Healing Surge with Master of the Elements
        if Var['UndulationCounterP'] < 2 or Var['undulation'] == 1 or Player:BuffRemains(S.MasterOfTheElementsBuff) < 3 then
            if S.HealingSurge:IsReady(Player) and S.MasterOfTheElements:IsAvailable() and Player:BuffStack(S.MasterOfTheElementsBuff) >= 2 then
                if CastCycleAlly(S.HealingSurge, Members, EvaluateHealingSurgeMasterOfTheElements) then
                    return "Healing Surge x Master Of The Elements"
                end
            end
        end

        -- Totemic Projection for Surging Totem repositioning
        if S.SurgingTotem:IsAvailable(nil, true) and S.TotemicProjection:IsReady(Player) and TotemFinder(S.SurgingTotem) then
            if #Var['HealingRaidTable'] == 0 then
                if S.SurgingTotem:CooldownRemains(nil, true) > 5 and TotemFinder(S.SurgingTotem, true) > 5 then
                    if CastMagic(S.TotemicProjection, nil, "108287-Magic", Settings['magicgroundspell_totemicprojection']) then
                        return "Totemic Projection";
                    end
                end
            end
        end

        -- Healing Rain / Surging Totem for group healing
        if (HealingEngine:MembersUnderPercentage(Settings['HR_underX_val']) >= Settings['HR_underX'] and Var['IsInCombat']) then
            -- Choose between Surging Totem and Healing Rain based on talents
            if S.SurgingTotem:IsAvailable(nil, true) then
                -- Surging Totem placement
                if S.SurgingTotem:IsReady(Player) then 
                    if CastMagic(S.SurgingTotem, nil, "444995-Magic", Settings['magicgroundspell_surgingtotem']) then
                        return "Surging Totem"
                    end
                end
            else
                -- Healing Rain placement
                if S.HealingRain:IsReady(Player) then
                    if CastMagic(S.HealingRain, nil, "73920-Magic", Settings['magicgroundspell_healingrain']) then
                        return "Healing Rain"
                    end
                end
            end
        end
        
        -- Undulation management
        if S.Undulation:IsAvailable() and Var['UndulationCounterP'] >= 2 then
            -- Option 1: Healing Surge for Undulation
            if Var['undulation'] == 1 then
                if S.HealingSurge:IsReady(Player) then
                    if CastCycleAlly(S.HealingSurge, Members, EvaluateHealingSurge) then
                        return "Healing Surge x Undulation"
                    end
                end
            
            -- Option 2: Healing Wave for Undulation
            elseif Var['undulation'] == 2 then
                if S.HealingWave:IsReady(Player) then
                    if CastCycleAlly(S.HealingWave, Members, EvaluateHealingWave) then
                        return "Healing Wave x Undulation"
                    end
                end
            
            -- Option 3: Auto-select based on mana
            elseif Var['undulation'] == 3 then
                if Player:ManaPercentage() > 35 then
                    if S.HealingSurge:IsReady(Player) then
                        if CastCycleAlly(S.HealingSurge, Members, EvaluateHealingSurge) then
                            return "Healing Surge x Undulation (Auto)"
                        end
                    end
                else
                    if S.HealingWave:IsReady(Player) then
                        if CastCycleAlly(S.HealingWave, Members, EvaluateHealingWave) then
                            return "Healing Wave x Undulation (Auto)"
                        end
                    end
                end
            end
        end

        -- Maintain Riptide on tanks even without Tidal Waves need
        if S.Riptide:IsReady(Player) then
            if CastCycleAlly(S.Riptide, Tanks, EvaluateRiptideUptime) then
                return "Riptide Up x Tanks"
            end
        end
        
        -- Core utility spells
        local ShouldReturn = Utilities()
        if ShouldReturn then
            return "Utilities: " .. ShouldReturn
        end

        -- Healing Surge for emergency healing
        if S.HealingSurge:IsReady(Player) then
            if CastCycleAlly(S.HealingSurge, Members, EvaluateHealingSurge) then
                return "Healing Surge"
            end
        end

        -- Healing Wave for efficiency
        if S.HealingWave:IsReady(Player) then
            if CastCycleAlly(S.HealingWave, Members, EvaluateHealingWave) then
                return "Healing Wave"
            end
        end

        -- Totemic Recall for cooldown reduction
        if S.TotemicRecall:IsReady(Player) then
            local TotemicRecallOptions = GetSetting('TotemicRecall', {})
            local TR_Totems = {}

            -- Process settings properly, handling both array and key-based tables
            if type(TotemicRecallOptions) == 'table' then
                for key, value in pairs(TotemicRecallOptions) do
                    if type(key) == 'number' then
                        -- If the table is an array
                        TR_Totems[value] = true
                    else
                        -- If the table is key-based
                        if value then
                            TR_Totems[key] = true
                        end
                    end
                end
            end

            -- Determine which totem to recall based on settings and cooldown status
            local TotemToRecall = nil
            if TR_Totems['recallHST'] and S.HealingStreamTotem:IsAvailable() and S.HealingStreamTotem:CooldownRemains(nil, true) > 7 and not Player:TotemIsActive(Var['HealingStreamTotemName']) then
                TotemToRecall = "Healing Stream Totem"
            elseif TR_Totems['recallST'] and S.SurgingTotem:IsAvailable() and S.SurgingTotem:CooldownRemains(nil, true) > 7 and not Player:TotemIsActive(Var['SurgingTotemName']) then
                TotemToRecall = "Surging Totem"
            elseif TR_Totems['recallEWT'] and S.EarthenWallTotem:IsAvailable() and S.EarthenWallTotem:CooldownRemains(nil, true) > 20 then
                TotemToRecall = "Earthen Wall Totem"
            end

            if TotemToRecall then
                if Cast(S.TotemicRecall) then
                    return "Totemic Recall x " .. TotemToRecall
                end
            end
        end  

        -- Auto double-dip Earthen Wall Totem (quickly placing another after the first)
        if S.EarthenWallTotem:IsAvailable() then 
            if S.EarthenWallTotem:IsReady(Player) and S.EarthenWallTotem:TimeSinceLastCast() < 5 then
                if Cast(S.EarthenWallTotem) then
                    return "Earthen Wall Totem (double dip)"
                end
            end
        end   

        -- Earthen Wall Totem for damage reduction
        if S.EarthenWallTotem:IsReady(Player) and HealingEngine:MembersUnderPercentage(Settings['EW_underX_val']) >= Settings['EW_underX'] then
            if Cast(S.EarthenWallTotem) then
                return "Earthen Wall Totem"
            end
        end
    end

    -- AoE damage rotation
    local function DamageRotationAoE()
        -- Acid Rain (Healing Rain with Acid Rain talent)
        if S.HealingRain:IsReady() and S.AcidRain:IsAvailable() and Target:TimeToDie() > 3 then
            if CastMagic(S.HealingRain, nil, "73920-Magic", Settings['magicgroundspell_healingrain']) then
                return "Healing Rain AoE"
            end
        end
        
        -- Chain Lightning with Stormkeeper buff
        if Player:BuffUp(S.Stormkeeper) then
            if S.ChainLightning:IsReady() then
                if Cast(S.ChainLightning) then
                    return "Chain Lightning x Stormkeeper"
                end
            end
        end
        
        -- Stormkeeper to empower next lightning spells
        if S.Stormkeeper:IsReady() and Target:TimeToDie() > 3 then
            if Cast(S.Stormkeeper) then
                return "Stormkeeper"
            end
        end
        
        -- Chain Lightning for AoE damage
        if S.ChainLightning:IsReady() then
            if Cast(S.ChainLightning) then
                return "Chain Lightning"
            end
        end
        
        -- Flame Shock for movement and DoT
        if S.FlameShock:IsReady(Player) then
            CountFlameShock()
            if CountFlameShock() < 6 then
                if CastCycle(S.FlameShock, Enemies40y, EvaluateCycleFlameShock) then
                    return "Flame Shock - Movement"
                end
            end
        end
    end

    -- Single target damage rotation
    local function DamageRotationST()
        -- Acid Rain if talented
        if S.HealingRain:IsReady() and S.AcidRain:IsAvailable() and Target:TimeToDie() > 3 then
            if CastMagic(S.HealingRain, nil, "73920-Magic", Settings['magicgroundspell_healingrain']) then
                return "Healing Rain ST"
            end
        end
        
        -- Lightning Bolt with Stormkeeper buff
        if Player:BuffUp(S.Stormkeeper) then
            if S.LightningBolt:IsReady() then
                if Cast(S.LightningBolt) then
                    return "Lightning Bolt x Stormkeeper"
                end
            end
        end
        
        -- Flame Shock for DoT
        if S.FlameShock:IsReady() and Target:DebuffRefreshable(S.FlameShockDebuff) then
            if Cast(S.FlameShock) then
                return "Flame Shock"
            end
        end
        
        -- Lava Burst for high damage and Master of the Elements
        if S.LavaBurst:IsReady() and Target:DebuffRemains(S.FlameShockDebuff) > S.LavaBurst:CastTime() then
            if Cast(S.LavaBurst) then
                return "Lava Burst"
            end
        end
        
        -- Stormkeeper to empower next lightning spells
        if S.Stormkeeper:IsReady() and Target:TimeToDie() > 3 then
            if Cast(S.Stormkeeper) then
                return "Stormkeeper"
            end
        end
        
        -- Lightning Lasso for additional damage/CC
        if S.LightningLasso:IsReady() then
            if Cast(S.LightningLasso) then
                return "Lightning Lasso"
            end
        end
        
        -- Lightning Bolt as filler
        if S.LightningBolt:IsReady() then
            if Cast(S.LightningBolt) then
                return "Lightning Bolt"
            end
        end
        
        -- Flame Shock for movement
        if S.FlameShock:IsReady(Player) then
            CountFlameShock()
            if CountFlameShock() < 6 then
                if CastCycle(S.FlameShock, Enemies40y, EvaluateCycleFlameShock) then
                    return "Flame Shock - Movement"
                end
            end
        end
    end

    -- Filler actions when no healing is needed
    local function Filler()
        -- Ghost Wolf for movement
        if not Var['GhostWolfBuff'] and not Var['SpiritwalkersGrace'] and S.GhostWolf:IsReady(Player) then
            local ShouldReturn = AutoWolf()
            if ShouldReturn then
                return "Defensives: " .. ShouldReturn
            end
        end
    end

    -- Main Action Priority List
    local function APL()
        -- Update variables and state
        UpdateVars()
        
        -- Handle enemy tracking based on AoE setting
        if AoEON() then
            Enemies40y = Player:GetEnemiesInRange(40)
            Enemies10ySplash = Target:GetEnemiesInSplashRange(10)
        else
            Enemies40y = {Target}
            Enemies10ySplash = {Target}
        end
        EnemiesCount10ySplash = #Enemies10ySplash

        -- Update environment state variables
        inCombat = Player:AffectingCombat()
        inDungeon = Player:IsInDungeonArea()
        inRaid = Player:IsInRaidArea()
        isMoving = Player:IsMoving()

        -- Skip rotation if in Ghost Wolf and setting enabled
        if Var['GhostWolfBuff'] and Settings['StopWolf'] then
            return
        end
              
        -- Use items and trinkets
        local ShouldReturn = Items();
        if ShouldReturn then
             return ShouldReturn;
        end 
        
        local shouldReturn = MainAddon.TrinketHealing(Members, OnUseExcludes)
        if shouldReturn then
            return shouldReturn
        end

        -- Handle mana potion use
        if MainAddon.UseManaPotion() then
            MainAddon.SetTopColor(1, "Combat Potion")
        end

        -- Force DPS mode when toggle is active
        if Var['TargetIsValid'] then
            if MainAddon.Toggle:GetToggle('ForceDPS') then
                if EnemiesCount10ySplash >= 2 then
                    local ShouldReturn = DamageRotationAoE();
                    if ShouldReturn then
                        return "Force DPS AoE Toggle: " .. ShouldReturn;
                    end
                else
                    local ShouldReturn = DamageRotationST();
                    if ShouldReturn then
                        return "Force DPS ST Toggle: " .. ShouldReturn;
                    end
                end
            end
        end

        -- Out of combat healing cooldowns
        local ShouldReturn = HealingCDsOOC()
        if ShouldReturn then
            return "Healing CDs: " .. ShouldReturn
        end

        -- In-combat defensives and major cooldowns
        if Var['IsInCombat'] then
            -- Defensive abilities
            local ShouldReturn = Defensives()
            if ShouldReturn then
                return "Defensives: " .. ShouldReturn
            end

            -- Major healing cooldowns
            local ShouldReturn = HealingCDsCombat()
            if ShouldReturn then
                return "Healing CDs: " .. ShouldReturn
            end
        end

        -- Special healing for focus/mouseover
        local ShouldReturn = HealingSpecial();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Handle incoming damage predictions
        local Reason, SpellID = MainAddon:DamageIncoming()
        if Reason == "SOON" then
            MainAddon.UI:ShowToast(MainAddon.GetSpellInfo(SpellID), "Predicting major incoming damage.", MainAddon.GetTexture(Spell(SpellID)), 10)

            local ShouldReturn = DamageIncoming();
            if ShouldReturn then
                return ShouldReturn;
            end
        end

        -- Auto Tremor Totem for fear effects
        if S.TremorTotem:IsReady(Player) then
            local ShouldReturn = Shaman.EvaluateTremor(S.TremorTotem)
            if ShouldReturn then
                return ShouldReturn
            end
        end

        -- Core healing rotation
        local ShouldReturn = HealingRotation()
        if ShouldReturn then
            return "Healing Rotation: " .. ShouldReturn
        end

        -- Utility spells when no healing needed
        local ShouldReturn = UtilitiesFiller()
        if ShouldReturn then
            return "Utilities: " .. ShouldReturn
        end

        -- DPS when target is valid and no healing is needed
        if Var['TargetIsValid'] then
            -- Master of the Elements management
            if S.MasterOfTheElements:IsAvailable() then
                if S.LavaBurst:IsReady() and (Player:BuffStack(S.MasterOfTheElementsBuff) < 2 or Player:BuffRemains(S.MasterOfTheElementsBuff) < 3) then
                    if Cast(S.LavaBurst) then
                        return "Lava Burst - Master of the Elements"
                    end
                end
            end

            -- Damage rotation based on target count
            if EnemiesCount10ySplash >= 2 then
                local ShouldReturn = DamageRotationAoE();
                if ShouldReturn then
                    return "Damage Rotation AoE: " .. ShouldReturn;
                end
            else
                local ShouldReturn = DamageRotationST();
                if ShouldReturn then
                    return "Damage Rotation ST: " .. ShouldReturn;
                end
            end
        end

        -- Filler actions when nothing else to do
        local ShouldReturn = Filler()
        if ShouldReturn then
            return "Filler: " .. ShouldReturn
        end
    end

    -- Initialize totem name variables
    local function Init()
        Var['CloudburstTotemName'] = S.CloudburstTotem:Name()
        Var['HealingStreamTotemName'] = S.HealingStreamTotem:Name() 
        Var['SurgingTotemName'] = S.SurgingTotem:Name() 
        Var['EarthenWallTotemName'] = S.EarthenWallTotem:Name()
    end
    
    -- Register the APL
    M.SetAPL(264, APL, Init)
    
    -- Event handling for group composition updates
    HL:RegisterForEvent(function()
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
        GetMembersGUID()
    end, "PLAYER_ENTERING_WORLD", "GROUP_ROSTER_UPDATE", "PLAYER_REGEN_ENABLED")

    -- Event handling for zone changes
    HL:RegisterForEvent(function()
        GetMembersGUID()
    end, "ZONE_CHANGED_NEW_AREA", "UPDATE_CHAT_WINDOWS", "PLAYER_ENTERING_WORLD")

    -- Combat log event processing for healing tracking
    HL:RegisterForEvent(function(...)
        local _, subevent, _, sourceGUID, _, _, _, destGUID, destName, _, _, spellId = CombatLogGetCurrentEventInfo()
        if sourceGUID == Player:GUID() then
            -- Track units affected by Healing Rain
            if spellId == 73921 and subevent == "SPELL_HEAL" then
                Var['HealingRaidTableTimeSince'][destGUID] = GetTime()
                if not tContains(Var['HealingRaidTable'], Var['MembersGUID'][destGUID]) then
                    table.insert(Var['HealingRaidTable'], Var['MembersGUID'][destGUID])
                end
            end

            -- Remove units from Healing Rain tracking after buff expires
            if Var['HealingRaidTableTimeSince'][destGUID] then
                if GetTime() - Var['HealingRaidTableTimeSince'][destGUID] > 2 then
                    for i = #Var['HealingRaidTable'], 1, -1 do
                        if Var['HealingRaidTable'][i] == Var['MembersGUID'][destGUID] then
                            table.remove(Var['HealingRaidTable'], i)
                        end
                    end
                end
            end

            -- Track Undulation counter for healing spell casts
            if subevent == "SPELL_HEAL" then
                if spellId == 77472 or spellId == 8004 then
                    Var['UndulationCounter'] = (Var['UndulationCounter'] + 1) % 3
                    MainAddon.db.profile.class[264].UndulationCounter = Var['UndulationCounter']
                end
            end
        end
    end, "COMBAT_LOG_EVENT_UNFILTERED")

    -- Load Undulation counter from saved variables
    HL:RegisterForEvent(function(self, event, isLogin, isReload)
        if isLogin then
            Var['UndulationCounter'] = MainAddon.db.profile.class[264].UndulationCounter or 0
        end
    end, "PLAYER_ENTERING_WORLD")

    -- Reset Undulation counter when spell overlay is hidden
    HL:RegisterForEvent(function(event, spellID)
        if spellID == 216251 then
            Var['UndulationCounter'] = 0
            MainAddon.db.profile.class[264].UndulationCounter = 0
        end
    end, "SPELL_ACTIVATION_OVERLAY_HIDE")

    -- Override for Undulation counter tracking
    HL.AddCoreOverride("Player.UndulationCounterP",
        function (self)
            if Var['UndulationCounter'] then
                -- Increment counter if actively casting a healing spell
                if (Player:IsCasting(S.HealingWave) or Player:IsCasting(S.HealingSurge)) then
                    return Var['UndulationCounter'] + 1
                end
                return Var['UndulationCounter']
            end

            return 0
        end
    , 264)

    -- Override for spell castability to handle movement and totem placement
    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                -- Allow casting while moving if Spiritwalker's Grace is active
                if Player:BuffUp(S.SpiritwalkersGrace) then
                    ignoreMovement = true
                end

                -- Prevent double-casting Healing Surge
                if self == S.HealingSurge then
                    if Player:IsCasting(S.HealingSurge) then
                        return false, 'Already casting'
                    end
                end

                -- Prevent totem placement when moving too much
                if self == S.HealingStreamTotem then
                    if Var['IsStandingStillFor'] < Settings['TotemMovingValue'] then
                        return false, 'Moving'
                    end
                end

                if self == S.CloudburstTotem then
                    if Var['IsStandingStillFor'] < Settings['TotemMovingValue'] then
                        return false, 'Moving'
                    end
                end

                if self == S.SurgingTotem then
                    if Var['IsStandingStillFor'] < Settings['TotemMovingValue'] then
                        return false, 'Moving'
                    end
                end

                if self == S.StoneBulwarkTotem then
                    if Var['IsStandingStillFor'] < Settings['TotemMovingValue'] then
                        return false, 'Moving'
                    end
                end

                -- Continue with normal castability check
                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                return BaseCheck, Reason
            end
    , 264);
end