"PRIEST",
"Discipline: Fixed an issue with Pet detection.",

"DEMONHUNTER",
"Vengeance: Rotation reworked!",
"Vengeance: Please reset your classdb.",
"Vengeance: Please redo your autobind.",
"Vengeance: Default settings updated.",
"Vengeance: Removed outdated settings and introduced new ones where neccessary.",
"Vengeance: Updated AoE rotation.",
"Vengeance: Updated ST rotation.",
"Vengeance: Updated Defensive handling.",
"Havoc: Rotation reworked!",
"Havoc: Please reset your classdb.",
"Havoc: Please redo your autobind.",
"Havoc: Default settings updated.",
"Havoc: Removed outdated settings and introduced new ones where neccessary.",
"Havoc: Updated AoE rotation.",
"Havoc: Updated ST rotation.",

"DEMONHUNTER",
"Vengeance: Fixed an issue where the rotation wasn't updating Trinkets correctly.",

"SHAMAN",
"Restoration: Prevent accidental double casts of Healing Surge.",

"DRUID",
"Balance: Improved Cooldown Sync.",
"EVOKER",
"Preservation: Fixed an issue where the rotation would cancel 'Emerald Communion'.",
"PRIEST",
"Discipline: Better handling of 'Penance' when 'Inescapable Torment' is talented.",

"DRUID",
"Balance: Added proper handling for Fluid Form.",

"WARLOCK",
"Affliction: Added a new setting to change what pet is used in Outdoor content.",
"Destruction: Added a new setting to change what pet is used in Outdoor content.",

"MONK",
"Brewmaster: Fixed a major issue with Breath of Fire usage.",

"MONK",
"Brewmaster: Rotation reworked!",
"Brewmaster: Please reset your classdb.",
"Brewmaster: Please redo your autobind.",
"Brewmaster: Default settings updated.",
"Brewmaster: Updated AoE rotation.",
"Brewmaster: Updated ST rotation.",
"Brewmaster: Updated Defensive handling.",
"Brewmaster: Improved offensive rotation during Weapons of Order.",
"Brewmaster: Improved the way we manage our Vitality when playing Master of Harmony.",
"Brewmaster: Added a new logic to make sure we handle Wisdom of the Will correctly when playing Shado Pan.",
"Brewmaster: Improved Celestial Brew and Black Ox Brew logics.",
"Brewmaster: Improved Purifying Brew logics when playing Gai Plins Imperial Brew and/or High Tolerance.",
"Brewmaster: Reworked Charred Passions logic.",
"Brewmaster: Reworked Press the Advantage logic.",
"Brewmaster: Black Ox Statue is now handled fully automatic.",
"Brewmaster: Improved threat generation at the start of a pull.",
"Brewmaster: We now cast Breath of Fire during prepull for additional threat.",

"MONK",
"Windwalker: Updated Trinket usage.",
"Windwalker: Updated Cooldown usage.",
"Windwalker: Updated opener for both AoE and ST.",
"Windwalker: Updated AoE rotation.",
"Windwalker: Updated Cleave rotation.",
"Windwalker: Improved Xuen synchronization.",
"Windwalker: Improved Storm, Earth and Fire synchronization.",
"Windwalker: Improved Crackling Jade Lightning usage when playing Emperors Capacitor.",
"Windwalker: Added a Toast Message when Crackling Jade Lightning is about to be used.",
"WARLOCK",
"Affliction: Updated Cooldown usage.",
"Affliction: Updated AoE rotation.",
"Affliction: Updated ST rotation.",  
"Affliction: Improved Cooldown usage.",
"Demonology: Small backend fix to Ruination and Hand of Guldan.",
"Demonology: Updated Cooldown usage.",
"Demonology: Updated AoE rotation.",
"Demonology: Updated ST rotation.",
"Demonology: Heavily improved Tyrant rotation.",
"Demonology: Improved Demonbolt usage.",
"Destruction: Updated Cooldown usage.",
"Destruction: Updated AoE rotation.",
"Destruction: Improved Rain of Fire usage.",
"SHAMAN",
"Enhancement: Custom rotation: Improved Doom Winds / Ascendance sync.",
"Enhancement: Custom rotation: Improved Primordial Storm usage.",
"Enhancement: Totemic: Improved AoE rotation.",
"Enhancement: Totemic: Improved ST rotation.",
"Elemental: Improved Opener/Precombat rotation.",
"Elemental: Updated Cooldown usage.",
"Elemental: Updated AoE rotation.",
"Elemental: Updated ST rotation.",  

"SHAMAN",
"Enhancement: Custom rotation: Improved Doom Winds / Ascendance sync.",
"Enhancement: Custom rotation: Fixed an issue that would cause Primordial Storm to run out.",
"Enhancement: Custom rotation: Improved Primordial Storm usage on low TTD packs.",
"DRUID",
"Balance: Fixed an issue where the rotation would wrongly assume a ST situation during AoE.",

"DRUID",
"Balance: We now spend 'Touch of the Cosmos' procs even with the Save AP toggle enabled.",

"DRUID",
"Balance: Fixed an issue where we would use both stacks of Incarnation at the same time.",

"SHAMAN",
"Enhancement: Changed default settings. You can reset your classdb if you want the new settings to auto apply or just keep your old settings.",

"DEATHKNIGHT",
"Unholy: Fixed a lua error related to the festering wound target count.",

"PRIEST",
"Discipline: Oracle Penance will now respect the 'Power Word: Shield' hp slider value. If you want the old 'use on cd' logic set the 'Power Word: Shield' slider to '100'.",
"DEATHKNIGHT",
"Unholy: Reworked ST rotation.",
"Unholy: Reworked AoE rotation.",
"Unholy: Reworked AoE setup.",
"Unholy: Reworked Cooldown usage.",
"DRUID",
"Balance: Reworked ST rotation.",
"Balance: Reworked AoE rotation.",
"Balance: Reworked Cooldown conditions.",
"MAGE",
"Arcane: Updated to latest simc APL. If you use custom rotation there are no changes for you as the custom rotation was already doing these things anyways.",

"PRIEST",
"Discipline: New setting to customize the pet usage based on your groups health.",

"SHAMAN",
"Enhancement: New setting: 'Tempest: Build stacks before Ascendance', enabled and set to '1' per default.",
"Enhancement: New toggle: 'PoolTempest'. Enabling this will allow you to manually pool Tempest Stacks.",
"Enhancement: Custom rotation: Heavily improved Ascendance rotation",
"Enhancement: Custom rotation: Heavily improved Ascendance setup",
"Enhancement: Custom rotation: Improved 'Doom Winds' usage.",
"Enhancement: Custom rotation: Made sure we don't waste 'Primal Storm' outside of Ascendance.",
"Enhancement: Custom rotation: Improved 'Primordial Wave' usage.",
"Enhancement: Custom Rotation: Improved 'Lightning Crash' usage.",
"DEATHKNIGHT",
"Frost: Improved Trinket usage.",
"Frost: Improved 'Frost Strike' usage in AoE scenarios.",
"Frost: Improved 'Cooldown rotation' (when under Pillar/Reapers Mark)",
"Frost: Reworked Single Target rotation.",
"Frost: Improved Runic Power pooling.",
"MONK",
"Brewmaster: Reworked Defensive logics.",
"Brewmaster: Improved Purify logic.",
"Brewmaster: (Hopefully) improved aggro build-up on pull start.",

"DEATHKNIGHT",
"Unholy: Custom Rotation: Improved 'Unholy Assault' syncs.",
"Unholy: Custom Rotation: Improved 'Dark Transformation' interactions with 'Unholy Assault'.",
"Unholy: Custom Rotation: Improved 'Defile/Death and Deacy' syncs.",
"Unholy: Changed default settings. You can reset your classDB if you want the new settings to be applied or just keep your current settings.",

"DEATHKNIGHT",
"Unholy: Custom Rotation: Fixed an issue with 'Unholy Assualt' sync.",

"DEATHKNIGHT",
"Unholy: Custom Rotation: Improved 'Unholy Assualt' usage when playing Sanlayn.",

"DEATHKNIGHT",
"Unholy: Removed 'Sync Dark Transformation/Unholy Assault' setting, its now baked into the custom APL.",
"Unholy: New setting to choose between 'SimC APL' and 'Custom Rotation'.",
"Unholy: Custom Rotation: Improved various syncs related to 'Unholy Assault', 'Dark Transformation' and 'Defile'.",
"Unholy: Custom Rotation: Improved 'Festering Scythe' and 'Festering Strike' usage.",

"DRUID",
"Balance: Improvements to Lunar Calling.",
"Balance: Improvements to cooldown sync.",
"Balance: Improvements to "Best-In-Slot" weapon usage.",
"Balance: Backend optimizations.",

"MONK",
"Mistweaver: Fixed an issue with 'Crackling Jade Lightning' when set to 'Auto'.",
"PRIEST",
"Discipline: Fixed an issue with Radiance pet sync when playing Oracle.",
"DEATHKNIGHT",
"Frost: 'Stand still treshold' setting will now also work in Bossfights.",
"Unholy: 'Stand still treshold' setting will now also work in Bossfights.",
"MONK"
"Windwalker: 'Stand still treshold' setting will now also work in Bossfights.",

"MONK",
"Mistweaver: Improved 'Soothing Mist' handling.",
"Mistweaver: Fistweaving: We don't 'Soothing Mist' anymore if multiple units are in critical need for healing.",
"Mistweaver: Fistweaving: We don't 'Soothing Mist' anymore if we could instant Vivify instead (Vivacious Vivification).",
"DEATHKNIGHT",
"Unholy: New setting to customize the target count at which we use 'Epidemic'. Disable to use default logic.",

"DEATHKNIGHT",
"Frost: Please reset your classdb.",
"Frost: Backend improvements.",
"Frost: New settings to customize 'Stand still treshold' and 'Melee ratio %'.",
"Frost: New setting: 'Use Anti-Magic Shell offensively', disabled by default.",
"Frost: Improved Cooldown usage.",
"Frost: Improved 'Remorseless Winter' syncronization with other Cooldowns.",
"Unholy: Please reset your classdb.",
"Unholy: Backend improvements.",
"Unholy: New settings to customize 'Stand still treshold' and 'Melee ratio %'.",
"Unholy: New setting: 'Use Anti-Magic Shell offensively', disabled by default.",
"MONK",
"Windwalker: Please reset your classdb.",
"Windwalker: Backend improvements.",
"Windwalker: New settings to customize 'Stand still treshold' and 'Melee ratio %'.",

"MAGE",
"Arcane: Improvements for Sunfury rotation.",

"MONK",
"Mistweaver: Fixed 'Soothing Mist' targeting.",

"MAGE",
"Arcane: Backend optimizations for Sunfury.",
"Arcane: Improved 'Touch of the Magi' usage when 'Arcane Surge' is ready.",
"MONK",
"Mistweaver: Fixed an issue regarding 'Soothing Mist' healing.",

"MAGE",
"Arcane: Full rework, custom rotation added. Please reset your classdb!",
"Arcane: New setting to choose between SimC APL and Custom Rotation.",
"Arcane: Removed outdated settings.",

"SHAMAN",
"Enhancement: Custom Rotation: Fixed desync issue for 'Doom Winds'.",
"Enhancement: Custom Rotation: Fixed an issue in regards to 'Primordial Storm' use.",
"MONK",
"Mistweaver: Backend optimizations.",
"MAGE",
"Arcane: Fixed issues with 'Touch of the Magi' debuff checks.",
"Arcane: Fixed an issue with 'Arcane Barrage' inflight calculations.",
"Arcane: Fixed an issue with 'Arcane Barrage' inflight calculations.",
"Arcane: Made sure we dont use 'Shifting Power' if 'Arcange Surge/Touch of the Magi' are still available.",


"SHAMAN",
"Enhancement: Removed unneccesary and outdated settings.",
"Enhancement: New default settings, will apply on class db reset.",
"Enhancement: Heavily improved 'Custom rotation' for both 'Totemic' and 'Stormbringer'.",
"Enhancement: Custom rotation: Improved 'Ascendance' sync.",
"Enhancement: Custom rotation: Improved 'Doom Winds' sync.",
"Enhancement: Custom Rotation: Improved 'Primordial Storm' usage.",
"Enhancement: Custom rotation: Improved 'Primordial Wave' usage.",

"MAGE",
"Arcane: Fixed an issue where the rotation clips Arcane Missles in Sunfury.",

"MAGE",
"Arcane: Fixed an issue where the rotation would skip over Arcane Surge if Time Anomaly was procced.",
"MONK",
"Mistweaver: Improved instant Vivifiy under Zen Pulse.",
"Mistweaver: Improved Black Ox usage if more then one member needs healing.",

"MONK",
"Mistweaver: Optimized 'Life Cocoon' usage on dangerous situations.",

"MAGE",
"Arcane: Improved 'Arcane Missles' usage during movement.",
"Arcane: Made sure we dont clip 'Arcane Missles' during movement.",
"Arcane: Improved 'Arcane Surge' logic.",
"Arcane: New Setting: 'Disable dispel and low priority kicks while under Arcane Surge', enabled by default.",

"MONK",
"Mistweaver: Fixed a lua error.",

"MAGE",
"Arcane: Further improved Arcane Missles clipping.",

"MAGE",
"Arcane: Improved AA clipping when 'Cut Arcane Missles only on close range' is enabled.",
"Arcane: Improved triggering of 'Magi's Spark'.",

"MAGE",
"Arcane: New setting: 'Cut Arcane Missles only on close range'. Makes sure you will only clip AA Missles if you are in melee range. Enabled by default",
"Arcane: Added a hard check to make sure we dont accidentally cast Arcane Missles under Nether Precision.",

"DRUID",
"Restoration: Rotation reworked!",
"Restoration: Backend optimizations.",
"Restoration: Completly reworked the Settings menu.",
"Restoration: Reset your Classdb.",
"Restoration: New default settings.",
"Restoration: Improved Catweaving.",
"Restoration: Improved Grove Guardians logic.",
"Restoration: Improved Wild Growth usage.",
"Restoration: Improved Soul of the Forest logic.",
"Restoration: Improved maintenance healing.",
"Restoration: Added logic for Heart of the Wild.",
"EVOKER",
"Preservation: Rotation reworked!",
"Preservation: Backend optimizations.",
"Preservation: Completly reworked the Settings menu.",
"Preservation: Improved 'Dream Breath' usage with 'Temporal Anomaly'.",
"Preservation: Improved 'Dream Breath' usage.",
"MONK",
"Mistweaver: Completly reworked the Settings menu.",
"Mistweaver: Backend optimizations.",
"Mistweaver: Reworked Mana Tea logic.",
"Mistweaver: Reworked Thunder Focus Tea logic.",
"Mistweaver: Added Enveloping Mist to auto usage on Thunder Focus Tea.",
"Mistweaver: Reworked Black Ox logic.",
"Mistweaver: Fixed 'Enveloping Mist' not being applied to party members in certain situations.",
"SHAMAN",
"Restoration: Completly reworked the Settings menu.",
"Restoration: Backend optimizations.",
"Restoration: Improved 'Undulation' logic.",
"Restoration: Improved overlap on 'Healing Stream Totem'.",
"Enhancement: Please reset your class db!.",
"Enhancement: New default settings, will apply on class db reset.",
"Enhancement: 'Added Custom rotation' setting.",
"Enhancement: Custom rotation: Improved conditions on when to enter Ascendance.",
"Enhancement: Custom rotation: Improved 'Doom Winds' handling.",
"Enhancement: Custom Rotation: Improved Maelstrom handling.",
"Enhancement: Custom rotation: Improved Lightning Rod spread.",
"Enhancement: Custom Rotation: Improved 'Doom Winds' usage.",
"PRIEST",
"Discipline: Completly reworked the Settings menu.",
"Discipline: Backend optimizations.",
"Discipline: Added Force DPS toggle.",
"Holy: Completly reworked the Settings menu.",
"Holy: Backend optimizations.",
"Holy: Changed Premonition logic.",
"Holy: Further Improved 'Lightweaver' uptime.",
"MAGE",
"Arcane: Added new setting for Touch of the Magi re-targeting.",
"Arcane: Improved AoE rotation.",

"MAGE",
"Arcane: Fixed a huge issue with AoE detection.",
"Arcane: More updates coming later today.",

"PRIEST",
"Holy: Added the new Power Infusion system from Discipline.",
"Holy: New setting: 'Lightweaver Heal'.",
"Holy: Improved 'Lightweaver' handling and upkeep.",
"Holy: 'Lightweaver' will now also be kept up out of combat.",

"DRUID",
"Feral: Removed outdated settings.",
"Feral: New setting: 'Sync Convoke with Berserk'. Disabled by default.",

"PRIEST",
"Discipline: Updated 'Power Word: Shield' handling! No more wrong Shields on tank or high hp party members (hopefully TM).",

"MONK",
"Windwalker: Optimized Cleave rotation.",

"PRIEST",
"Discipline: Fixed wrong sorting for Tanks while evaluating 'Power Word: Shield'.",
"Discipline: Added a setting to customize 'Power Word: Shield' for the Tank when playing Oracle.",

"PRIEST",
"Discipline: Fixed 'Power Word: Shield' detection for Tanks.",
"Discipline: Improved 'Power Word: Shield' target selection, part 2.",
"Discipline: New Setting: Power Word: Barrier'.",

"PRIEST",
"Discipline: Fixed a big issue with 'Dark Reprimand' causing the rotation to idling.",
"Discipline: Implemented a "Anti-Stuck" feature if we ever desync 'Penance' and 'Power Word: Shield' (Oracle).",
"Discipline: Improved 'Power Word: Shield' target selection.",
"Discipline: Improved auto logic for 'Premonition of Insight'.",

"PRIEST",
"Discipline: Added fallback mechanisms to Premonition of Insight logic to prevent idling.",
"Discipline: Fixed 'Power Word: Shield' checks for Weal and Woe.",
"MONK",
"Windwalker: Optimized 'Unity Within' logic.",

"PRIEST",
"Discipline: Rotation reworked!",
"Discipline: Backend optimizations.",
"Discipline: Reset your Classdb.",
"Discipline: Cleaned up settings.",
"Discipline: New default settings.",
"Discipline: New offensive rotations for 'Oracle' and 'Voidweaver'.",
"Discipline: Healing priorities revised for both 'Oracle' and 'Voidweaver'.",
"Discipline: Revised Cooldown management.",
"Discipline: New logics for all Premonitions and Clairvoyance.",
"Discipline: Improved Entropic Rift logic.",
"Discipline: Improved Pet synchronisation .",

"DEATHKNIGHT",
"Unholy: Optimized 'Outbreak' usage.",
"Unholy: Optimized 'Wound Spender' usages.",

"DEATHKNIGHT",
"Unholy: Fixed a major issue in Epidemic target calculations.",
"Unholy: Optimized 'Festering Strike' usage.",

"DRUID",
"Balance: New Toggle: 'Save Astral Power' Can be toggled on/off with '/toggle SaveAP'.",
"Balance: New Setting: 'Pool Astral Power for next pull' enabled by default.",

"PRIEST",
"All Specs: Fixed 'Right Click Power Infusion' for all specs.",
"Holy & Discipline: The 'Auto Power Infusion' WeakAura has been updated! Please re-import it.",
"Discipline: Ramp toggle auto changes rotation based on if you are playing 'Voidweaver' or 'Oracle'.",
"Discipline: Updated 'Power Word: Shield' priority when playing Oracle.",
"Holy: Revised for Season 2.",
"Holy: Slightly changed priorities on Holy Fire.",

"MONK",
"Mistweaver: Fixed 'Stop Casting' to respect its set values.",
"Mistweaver: New Setting: 'Keep Enveloping Mist up on the tank', disabled by default.",

"MONK",
"Brewmaster: Please reset your class db!",
"Brewmaster: Updated defensive handling.",
"Brewmaster: Updated default settings.",
"Brewmaster: Updated Purifying Brew logic.",
"Brewmaster: Improved logic for Dampen Harm.",
"Brewmaster: Improved logic for Fortifying Brew.",
"Brewmaster: Imrpoved logic for Ring of Peace on danger.",
"Brewmaster: Improved logic for Purifying Brew.",
"Brewmaster: New Setting: 'M+ Logics', enabled by default.",
"Brewmaster: New Setting: 'Niuzao', set to 'Offensive' by default.",
"Brewmaster: New Setting: 'Breath of Fire', set to 'Defensive' by default.",
"Brewmaster: Updated Single Target and AoE rotations.",
"Brewmaster: Improved Blackout Combo and Charred Passions interactions.",
"Brewmaster: Added support for the 11.1 Class Set 4pc bonus.",
"Brewmaster: Optimized Spinning Crane Kick usage.",

"MAGE",
"Fire: Fixed an issue where Hot Streak was not being properly spent on Pyroblast when Mouseover-Option was enabled and we are without a mouseover target.",
"Fire: Fixed an issue where 'Waiting for Ice Floes' message was showing even when Ice Floes is not talented.",

"SHAMAN",
"Enhancement: Added a setting to improve "Doom Winds x Primordial Wave" synchronization, enabled by default.",
"MAGE",
"Fire: Improved Fire Blast usage during Hyperthermia to maintain Feel the Burn buff.",
"Fire: Improved Combustion Pooling feature.",
"Fire: Added a fallback to (hopefully) prevent using a builder with 'Heating Up' on higher latency.",
"MONK",
"Windwalker: Added a setting to show a Toast Message whenever 'Crackling Jade Lightning' is coming up, enabled by default.",
"Windwalker: Optimized 'Celestial Conduit' usage.",
"Mistweaver: 'Crackling Jade Lightning' toast will now be shown ahead of the actual cast, reducing wasted casts due to movement.",
"Mistweaver: Improved 'Mana Tea' auto-cancellation logic with smarter conditions.",
"DRUID",
"Feral: Added trinket sync logic for 'Berserk' with a new setting. Disabled by default'.",


"PRIEST",
"Discipline: Fixed a typo causing wrong 'Penance' usage.",
"Discipline: Improved 'Shadow Word: Death' usage.",
"Discipline: Improved offensive 'Penance' usage.",
"Discipline: Removed hardcoded 'Penance' TTD.",
"MONK",
"Brewmaster: Blocked 'Ring of Peace' during Captain Dailcry encounter.",
"Windwalker: Optimized 'Slicing Winds' usage.",  
"Windwalker: Optimized 'Whirling Dragon Punch' usage.", 
"Windwalker: Optimized ST rotation.",
"Windwalker: Fixed 'The Emperor's Capacitor'.",
"Mistweaver: Full rework.", 
"Mistweaver: Please reset your classdb.", 
"Mistweaver: Removed 'Full Auto' setting. This is baked into the rotation by default now. Check the Wiki for more info.'.", 
"Mistweaver: New 'Thunder Focus Tea' logic.", 
"Mistweaver: Removed old DPS rotation, replaced with 'Maintenance Healing' rotation.", 
"Mistweaver: Added 'Force DPS' rotation.", 
"Mistweaver: 'Sheiluns Gift' now always respects the 'minimum Charges' setting.'.", 
"Mistweaver: Added a setting to show a Toast Message whenever 'Crackling Jade Lightning' is coming up, enabled by default.", 
"Mistweaver: Added a setting to automatically use 'Diffuse Magic' in Mythic+ Dungeons on specific debuffs, enabled by default.", 
"MAGE",
"All specs: Added 'Counterspell' to the Auto Target Swap settings.",

"MONK",
"Brewmaster: 'Ring of Peace on danger' fix.", 

"SHAMAN",
"Elemental: Backend stuff.",
 
"MONK",
"Windwalker: Added support for 'Signet of the Priory'.", 

"SHAMAN",
"Enhancement: Improved Syncing of 'Doom Winds', 'Feral Spirit' and 'Primordial Wave'.",
"Elemental: Backend stuff.",

"MAGE",
"Fire: Added a second check to prevent 'Shifting Power' while still under 'Combustion/Hyperthermia'.",
 
"DRUID",
"Restoration: Added combat check to 'Symbiotic Relationship'.",

"MAGE",
"All specs: Updated 'Greater Invisibility' logic for Season 2 Dungeon.",
"Fire: New setting: 'Shifting Power priority'. Lets you choose if you prefer to use SP for Combustion cooldown reduction or prefer not to waste PF/IB charges.",
"Fire: Backend stuff.",
 
"PRIEST",
"Discipline: Reworked 'Emergency Healing'."
"Discipline: Reworked 'Pain Suppresion'.",
"Discipline: Cleaned up settings.",

"PRIEST",
"Discipline: Rotation updated to s2!.",
"Discipline: Yep, you guessed it: reset your class db. sorry.",
"Discipline: Removed anything related to 'Purge the Wicked'.",
"Discipline: Removed anything related to 'Rapture'.",
"Discipline: Added 'Encroaching Shadows' logic.",
"Discipline: New setting: 'Always keep one Charge of Radiance for manual usage'. Disabled by default.",
"Discipline: New settings for 'Evangelism'.",
"Discipline: Reworked 'Special healing/MO' logic.",
"Discipline: Reworked 'Incoming damage' logic.",
"Discipline: Reworked 'Cooldown' logic.",
"Discipline: Reworked 'Healing' logic.",
"Discipline: Reworked 'DPS Rotation' logic.",


"SHAMAN",
"Enhancement: Please reset your class db!",
"Enhancement: Seriously...reset it.",
"Enhancement: Reworked complete rotation.",
"Enhancement: Changed default settings.",
"Enhancement: 'Custom Rotation' setting removed, this is now always enabled.",
"Enhancement: Backend optimizations.", 
"Enhancement: Added 'Opener' rotations for 'Totemic' and 'Stormbringer'.",

"MAGE",
"Fire: Made sure we never cast 'Shifting Power' while still under 'Combustion/Hyperthermia'.",
"MONK",
"Windwalker: Removed MO related 'Fists of Fury' and 'Whirling Dragon Punch'.", 
"Mistweaver: Another optimization for 'Mana Tea' usage on critically low mana values.",
"DRUID",
"Feral: New setting: 'Disable dispel and low priority kicks while in CDs.', enabled by default.",
"MAGE",
"Arcane: Updated to season 2!.",
 
"DRUID",
"Restoration: Reintroduced 'Pool for Forestation' toggle.",

"MAGE",
"Frost: Backend optimizations.",
"Frost: Improved 'Ice Lance' during Cleave and AoE situations.",
"Frost: Improved 'Ray of Frost' usage.",
"Frost: Improved 'Frozen Orb' usage.",
"Frost: Improved everything related to 'Winters Chill'.",
"MONK",
"Windwalker: Complete rework once again...", 
"Windwalker: Improved Cooldown usage.", 
"DEATHKNIGHT",
"Unholy: 'Stand still treshold' fixed.",

"DEATHKNIGHT",
"Unholy: New setting: 'Stand still treshold', enabled for 'DnD/Defile' by default.",
 
"MONK",
"Windwalker: The hopefully last try to fix the idle issue.", 

"MONK",
"Mistweaver: Optimized 'Mana Tea' usage on critically low mana values.",
 
"DEATHKNIGHT",
"Unholy: New setting: 'Festering Scythe: Pool for next pull', disabled by default.",
 
"MONK",
"Windwalker: Second try on fixing a idle issue.", 
 
"MONK",
"Windwalker: First try on fixing a idle issue.",
 
"MONK",
"Windwalker: Simplified some casting conditions.",

"DRUID",
"Restoration: Updated to season 2!",
"Restoration: Please reset your class db!",
"Restoration: Major rework!",
"Restoration: Reworked Lifebloom system. Automatically swaps priorities based on Talents, Raid/Dungeon, Playstyle now.",
"Restoration: Removed 'Dynamic Lifebloom' setting, not needed anymore.",
"Restoration: Removed 'Dynamic Cenarion Ward' setting, replaced with 'Keep Cenarion Ward on Tank'. Disabled by default." 
"Restoration: Reworked 'Ramp' toggle. We now correctly ramp in m+ and raids.",
"Restoration: Added 'Symbiotic Relationship' support.",
"Restoration: Reworked 'Special Healing' (Mouseover and NPC healing).",

 
"MONK",
"Windwalker: Major rework!",
"Windwalker: Improved ST, AoE and Cleave rotations.",
"Windwalker: Limited 'Vivacious Vivification' healing to dungeons. We are not offhealers.",
"Windwalker: Added a new setting: 'Save Touch of Karma for defensives'. Enabled by default.",
"Windwalker: Both 'SimC APL' and 'Custom Rotation' are updated now!",
 
"SHAMAN",
"Enhancement: Updated to season 2! First revision, this WILL need changes over the upcoming days.",
"Enhancement: The APL is NOT updated for now! Please use 'Custom Rotation'.",
"Enhancement: Please reset your class db!",
"Enhancement: Backend optimizations.",
"Enhancement: Removed 'Ancestral Guidance'.",  
"Enhancement: Added support for 'Primordial Storm'.",
"Enhancement: Added 'Caster' playstyle",

"MAGE",
"Frost: Simplified 'Splinterstorm' tracking.",
"Frost: Improved 'Ice Lance' during Cleave situations.",
"Frost: Removed debug print.",

"MAGE",
"Frost: Updated to season 2!,
"Fire: Backend optimizations.",
"MONK",
"Mistweaver: Backend optimizations.",

"SHAMAN",
"Elemental: Updated to season 2! This is a first draft, will be further improved once the APL is updated.",
"Elemental: Please reset your class db!",
"Elemental: Changed default settings.",
"Elemental: Removed 'Custom Rotation' setting, this is now enabled by default.",
"Elemental: Added a new setting to choose between the custom rotation and simc APL, set to custom by default.",

"MONK",
"Mistweaver: Updated to season 2!",
"Mistweaver: Please reset your class db!",
"Mistweaver: Backend optimizations.",
"Mistweaver: Removed outdated and not needed settings to clean up the settings menu.",  
"Mistweaver: Changed default settings.",
"Mistweaver: Added 'Caster' playstyle",
"Mistweaver: Added 'Full Auto' settings for 'Caster' playstyle.",
"Mistweaver: Revised ramp logics.",
"Mistweaver: Revised healing logics.",
"Mistweaver: Revised Chi-Ji and Yu'Lon logics and rotations.",
"Mistweaver: Revised mouseover healing logic.",

"MAGE",
"Fire: Improved 'Scorch' in sniping situations.",
"Fire: Improved 'Scorch' inside 'Combustion''.",
"Fire: Improved 'Scorch' outside 'Combustion''.",
 
"DRUID",
"Balance: Updated to season 2!",
 
"MAGE",
"Fire: Improved 'Improved Scorch' handling...heh.",
"Fire: Backend stuff.",

"DEATHKNIGHT",
"Frost: Updated to season 2!,
"Frost: Please reset your Class Database.",
"Frost: Changed default settings.",
"Frost: Removed outdated and not needed settings to clean up the settings menu.",  
"Frost: Removed custom rotation as its not updated for season 2. Moved some of the settings to the regular DPS settings.",
"Unholy: Updated to season 2!,
 
"MAGE",
"Fire: Updated to season 2! First revision.",
"Fire: Please reset your Class Database.",
"Fire: Changed default settings.",
"Fire: Removed outdated and not needed settings to clean up the settings menu.",
"Fire: Added a setting to ignore dispels and low prio kicks while we are in 'Combustion', enabled by default.",
"Fire: Updated Combustion handling when playing "Sunfury".,
"Fire: Updated Combustion handling when playing "Frostfire".,
 
"MONK",
"Brewmaster: Updated to season 2!",
"Brewmaster: Please reset your class db!",
"Brewmaster: Changed default settings.",
"Brewmaster: When playing "Blackout Combo" made sure we priotize 10%DR over "Charred Passions" now.",
"Brewmaster: Reworked opener.",
"Brewmaster: Reworked "Weapons of Order" rotation.",

"MONK",
"Windwalker: Updated to season 2!",
"Windwalker: Please reset your class db!",
"Windwalker: No more "Mark of the Crane", we are free! FREE!!!",
"Windwalker: Changed default settings.",
"Windwalker: Removed 'Custom Rotation' setting, this is now enabled by default.",
"Windwalker: Added a new setting to choose between the custom rotation and simc APL, set to custom by default.",
"Windwalker: Added 'Party Healing' to 'Vivacious Vivification'.",

"SHAMAN",
"All Specs: Backend stuff in preparation for season 2.",
"Enhancement: Optimized 'Crash Lightning' upkeep before entering 'Ascendance'.",
"Enhancement: Optimized 'Primordial Wave' during 'Ascendance' in single target.",
"MAGE",
"All Specs: Backend stuff in preparation for season 2.",
"MONK",
"All Specs: Backend stuff in preparation for season 2.",
"DEATHKNIGHT",
"Frost: Made sure we also dont 'Frost Strike' during BoS even with Custom Rotation disabled.,
 
"DEATHKNIGHT",
"Frost: New setting: "Breath of Sindragosa safety treshold". Allows you to choose how "safe" you want to play BoS, aka how much potential damage are you willing to sacrifice for upkeep? Set to 'low' by default.,
"SHAMAN",
"Enhancement: Small changes to 'Primordial Wave' usage.",

"WARLOCK",
"Destruction: New setting 'Low Shadowburn priority'. Disabled by default.",
 
"SHAMAN",
"Enhancement: Made sure we use 'Primordial Wave' even during Ascendance.",
 
"EVOKER",
"Augmentation: Fixed an issue in regards to 'Breath of Eon's spellID when playing Scalecommander.",

"PRIEST",
"Discipline: Added the 'Power Infusion WeakAura' to Discipline settings.",

"PRIEST",
"Discipline & Holy: New Setting: 'Power Infusion'. Set to 'Full Auto' by default.",
"Discipline & Holy: New 'Full Auto' logic for 'Power Infusion'. Please import the WeakAura from the settings menu to have this working. If you don't install the WeakAura the rotation will default to 'Old logic'.",
"DRUID",
"Balance: Backend optimizations.",

"DRUID",
"Balance: Fixed an issue with MO icons for Sunfire and Moonfire.",
"Balance: New setting: 'DoT units during movement', enabled by default.",
"WARLOCK", 
"Affliction: Custom rotation added. This is a first draft, feedback is highly appreciated.",  
"Affliction: Custom rotation: Changed opener to Seed of Corruption in AoE.",  
"Affliction: Custom rotation: Improved Vile Taint usage.",  
"Affliction: Custom rotation: Improved Soul Rot sync.",  
"Affliction: Custom rotation: Improved Malevolance usage outside of Darkglare windows .",  
"Affliction: Custom rotation: Improved Drain Soul usage.",  
"Affliction: New Toggle: Spread Agony. Will spread Agony to your pull until turned off again or all units have Agony up.",

"SHAMAN",
"Enhancement: Pushed a potential fix for "Check for enemies in melee" in Bossfights.",
"MONK",
"Windwalker: Pushed a potential fix for "Check for enemies in melee" in Bossfights.",


"PRIEST",
"Holy: New toggle 'Force Healing' You can kinda figure out what this does i guess :p.",
"SHAMAN",
"Enhancement: Please reset your class db!.",
"Enhancement: New default settings, will apply on class db reset.",
"Enhancement: Custom Rotation: 'Tempest: Pool for next pull' now also respects 'Primordial Wave'.",
"Enhancement: Custom rotation: Removed 'Primordial Wave - Prevent usage above X Maelstorm Stack' setting.
"Enhancement: Custom rotation: Removed 'Spread Lightning Rod debuff' setting. This is now a dropdown menu.",
"Enhancement: Custom Rotation: New Setting: Ascendance conditions. You can choose if you want to build and spend 'Primordial Wave x Tempest' (Strict) before entering Ascendance in AoE or not (Loose). Set to 'Loose' by default.",
"Enhancement: Custom rotation: New Setting: 'Spread Lightning Rod with:' You can choose with spells you want to spread Lightning Rod with.",
"Enhancement: Custom Rotation: New Setting: 'Disable Dispel and Thunderstorm while in Ascendance'. Enabled by default.",
"Enhancement: Custom Rotation: Made sure we dont desync 'Doom Winds', even if 'Ascendance' is set to CD toggle and turned off.",


"SHAMAN",
"Enhancement: Fixed a rare case where the rotation was using 'Chain Lightning' at max Maelstrom with Tempest available.",
"Enhancement: Fixed a rare case where the rotation was using 'Windstrike' at max Maelstrom with Tempest available.",

"SHAMAN",
"Enhancement: Please reset your class db!.",
"Enhancement: Custom rotation: Improvements to 'Sundering' when playing 'Totemic'.",
"Enhancement: Custom rotation: Another try at syncing 'Doom Winds' and 'Ascendance' better (We delay for 2 GCDs now).",
"Enhancement: Custom rotation: Improvements to our 'Primordial Wave x Lightning Rod' windows in AoE.",
"Enhancement: Custom rotation: Made sure we dont send 'Tempest' if 'Primordial Wave' is ready in the next 2 GCDs.",
"Enhancement: Custom rotation: Improved 'Primordial Wave' usage in AoE scenarios.",
"Enhancement: Removed 'Doom Winds' from the 'Check for enemies in melee (in %)' setting.",
"Enhancement: Added 'Sundering' to the 'Check for enemies in melee (in %)' setting.",
"Enhancement: Added 'Surging Totem' to the 'Check for enemies in melee (in %)' setting.",

"SHAMAN",
"Enhancement: Custom rotation: More optimizations for 'Lava Lash' and 'Doom Winds'.",

"SHAMAN",
"Enhancement: Custom rotation: Removed a missed check for 'Lava Lash'.",
 
"SHAMAN",
"Enhancement: Custom rotation: Improved syncing of Doom Winds with Ascendance.",

"SHAMAN",
"Enhancement: Custom rotation: Completely reworked rotation.",
"Enhancement: Custom rotation: New setting to spread Lightning Rod debuff (disabled by default).",
"Enhancement: Custom rotation: New toggle to disable Lightning Rod spread temporarily when 'spread Lightning Rod debuff' is enabled.",
"Enhancement: Custom rotation: New opener sequence.",
"Enhancement: Custom rotation: Lowered Lava Lash priority outside of the opener.",
"Enhancement: Custom rotation: Improved syncing of Doom Winds with Ascendance.",
"Enhancement: Custom rotation: Delayed Doom Winds for 1–2 GCDs during Ascendance to improve overlap duration.",
"Enhancement: Custom rotation: Ensured Primordial Wave buff isn't accidentally consumed by Lightning Bolt.",
"Enhancement: Custom rotation: Improved Voltaic Blaze usage for better Flame Shock spreading.",
"Enhancement: Custom rotation: Improved Feral Spirits usage.",
"Enhancement: Custom rotation: Improved Thorim's Invocation handling.",
"Enhancement: Custom rotation: Improved Arc Discharge logic.",
"Enhancement: Custom rotation: Improved Ascendance rotation."
"DRUID",
"Balance: Fixed 'Spymaster's Web' not respecting its logic.",
"WARLOCK", 
"All specs: Added 'Demonic Gateway' to the spell list for queueing purposes.",  
"All specs: Added 'Demonic Circle' to the spell list for queueing purposes.",  
"All specs: Added 'Demonic Circle: Teleport' to the spell list for queueing purposes.",     

"DRUID",
"Balance: 'Convoke the Spirits' should now be used correctly in the first CA window in single target.",

"MONK", 
"Mistweaver: Fixed Enveloping Mist during Chi-Ji windows.",  

"MONK", 
"Mistweaver: Fixed accidental usage of 'Sheilun's Gift' usage during our Chi-Ji window.",  
"Mistweaver: Fixed accidental usage of 'Crackling Jade Lightning' usage during our Chi-Ji window.",  
"Mistweaver: Potential fix for the 'Mana Tea' issue.", 
"Mistweaver: Full auto: Fixed 'Sheilun's Gift' beeing used with too little stacks.",       
"Mistweaver: Fixed 'Jade Empowerment' usage on single target.",    
"Brewmaster and Windwalker: Added Season 2 Tierset bonuses.",   

"PRIEST",
"Discipline: Updates to the 'Entropic Rift' rotation.",
"Discipline: Changed 'Power Word: Shield' handling.",
"MONK", 
"Mistweaver: Updates to 'Soothing Mist'.",  
"Mistweaver: Added a logic for 'Strength of the Black Ox'. No settings needed, will be handled full auto.", 
"Mistweaver: Completly reworked the 'Jade Empowerment logic'.", 
"Mistweaver: Added a setting: 'Jade Empowerment handling'.",  
"Mistweaver: Added sliders to customize the 'Jade Empowerment' healing'.", 
"Mistweaver: Upped the priority of 'Sheilun's Gift' by a lot when multiple party members are in need of healing.", 
"Mistweaver: New Setting:Auto Life Cocoon on tank.",
"Mistweaver: New Setting: 'Full auto mode' This is still in testing and i will need your feedback for it.", 

"SHAMAN",
"Enhancement: Added 'Feral Lunge' to the spell list.",
"Enhancement: Added 'Poison Cleansing Totem' to the spell list.",
 
"PRIEST",
"Discipline: Fixed an issue with Penance during Entropic Rift.",
"Discipline: Removed "Raid Ramp" feature as it was not working as intended.",
"Discipline: Added a simplified "Raid Ramp".",
"Discipline: Added a priority system to 'Rapture'.",
"Holy: New 'Premonition of Piety' logic.",
"Holy: New 'Prayer of Mending' logic.",
"Holy: Improved 'Lightweaver' rotation.",

"SHAMAN",
"Elemental: Custom Rotation: New conditions for entering Ascendance on single target situations.",

"PRIEST",
"Discipline: New logic for Entropic Rift.",
"Discipline: Reworked Healing priorities.",     
"Discipline: Changed 'Shadow Word: Death' handling.",
"Discipline: Changed 'Auto Rapture' logic.",    
"MONK",
"Windwalker: Custom rotation: New setting 'Check for enemies in melee'.",  
"Windwalker: Reworked 'Custom Rotation' settings.", 
"SHAMAN",
"Enhancement: Custom rotation: New setting 'Check for enemies in melee'.",  
"Enhancement: Reworked 'Custom Rotation' settings.",

"PRIEST",
"Discipline: Reset your classdb.",
"Discipline: Reworked the rotation.",     
"Discipline: Reworked Atonement handling.",   
"Discipline: Reworked Rapture handling.",  
"Discipline: Reworked Mindbender handling.",       
"Discipline: Changed default settings.",     
"Discipline: Cleaned up the Setting. Some obsolete settings are removed, some renamed, some added.",

"DEATHKNIGHT",
"Frost: Reset your classdb.",
"Frost: Custom rotation now available! Disabled by default.",
"Frost: Custom rotation: New setting 'Stand still treshold'.",    
"Frost: Custom rotation: New setting 'RP to start Breath of Sindragosa'.",  
"Frost: Custom rotation: New setting 'Rime RP treshold'.",  
"Frost: Custom rotation: New setting 'Check for enemies in melee'.", 
"PRIEST",
"Discipline & Holy: Removed obsolete WeakAuras from the settings.",
"Discipline & Holy: Made sure we dont PI healers and tanks anymore.",
"Discipline: Revised the auto pi logic.",
"Holy: Added auto pi logic.",
"SHAMAN",
"Elemental: Trying a fix for delayed Ascendance in ST.",

"PRIEST",
"Discipline: Removed 'Penance' from the filler logic.",
 
"SHAMAN",
"Restoration: Changes to Tidal Waves logic in Raid.",
"Elemental: Updated ST rotation.",
"MAGE",
"Frost: Changed 'Cone of Cold' handling.",
"Fire: Added a setting to customize the target count at which we start to use 'Flamestrike', disabled by default.",
"MONK",
"Mistweaver: Made sure we dont use 'Crackling Jade Lightning' inside our Master of Harmony windows.",       
"Mistweaver: New logic for 'Thunder Focus Tea' when specced into Master of Harmony.",   

"SHAMAN",
"Elemental: Custom Rotation: New setting to customize the pooling for 'Ascendance'.",
"MAGE",
"Fire: Changed the way we handle 'Flamestrike'/'Pyroblast' depending on target counts.",

"MAGE",
"Frost: Added 'Treacherous Transmitter' precombat.",
"Frost: Reworked Trinket handling for all major trinkets.",
"Frost: Updated ST, AoE and Cleave rotations to reflect latest changes to the spec.",
"Fire: Updated ST, AoE and Cleave rotations to reflect latest changes and damage buffs to Frostfire.",
"MONK",
"Windwalker: Changed 'Blackout Kick' priorities all throughout the rotation.",
"SHAMAN",
"Enhancement & Elemental: Custom Rota: We now sync 'Ancestral Guidance' with 'Ascendance' if possible because its pretty much zero healing outside of it.",
"MONK",
"Mistweaver: Please reset your class database.",
"Mistweaver: Removed 'Keep Chi Burst to reset Faeline Stomp' setting.",
"Mistweaver: Removed 'Faeline Stomp - Range' setting. Rotation auto checks the valid range.",
"Mistweaver: Renamed 'Force Yu'lon in melee range' setting.",
"Mistweaver: Changed DPS rotation.",
"Mistweaver: Changed Chi-Ji rotation.",
"Mistweaver: Changed auto-ramp rotation.",
"Mistweaver: Reworked 'Soothing Mist' settings.",

"SHAMAN",
"Restoration: Renamed 'Tidal Waves' settings to avoid confusion.",
"Restoration: Changed default values for 'Tidal Waves'.",
"DEATHKNIGHT",
"Unholy: Fixed a potential lua error that would occur if you summon your pet while falling (dont ask...).",
"Frost: Small change to high priority 'Howing Blast'.",       
"MAGE",
"Frost: Synced benefitial racials with 'Icy Veins'.",
"Frost: Updated AoE rotations (Frostfire, Spellslinger).",
"Frost: Updated ST rotations (Frostfire, Spellslinger).",
"Frost: Updated Cleave rotation (Frostfire).",
"Fire: Removed a setting to choose on how many targets we start using Flamestrike. Not needed anymore, rotation handles that automatically.",
"Fire: We dont use Barrier anymore during 'Combustion' if were not in any danger.",

"DEATHKNIGHT",
"Frost: Changed 'Death and Decay' to be cast at Player by default.",

"DEATHKNIGHT",
"Unholy: Added a setting to use 'Anti-Magic-Barrier' offensively to fish for runic power, disabled by default.",
"Unholy: Updated AoE rotation (San'layn and Rider of the Apocalypse).",
"Unholy: Updated ST rotation (San'layn and Rider of the Apocalypse).",
"Unholy: Updated Cleave rotation (San'layn and Rider of the Apocalypse).",       
"Unholy: Updated rotation during burst windows. (San'layn and Rider of the Apocalypse).",
"Unholy: We now summon our pet correctly even when out of combat.",
 "MONK",
"Mistweaver: Added automatic cancelling of 'Mana Tea' outside of the full auto logic.",
 
 "SHAMAN",
"Enhancement: Fixed an issue in regards to 'Skardyns Grace'.",

-- SH_Spells.lua: moved TreacherousTransmitter from Item.Shaman.Custom table to Item.Shaman.Commons (tww trinkets) table --

"SHAMAN",
"Enhancement: Improved syncing of 'Treacherous Transmitter' with 'Ascendance'.",
"PALADIN",
"Holy: Updated 'Lay on Hands' spell id.",
"MONK",
"Mistweaver: Added a setting to save 'Chi Burst' for 'Faeline Stomp' resets, disabled by default.",
"Mistweaver: Added 'Chi Burst' to the dps rotation. Be careful not to add stuff with it.",
"Mistweaver: Added a toast message whenever we are about to use buffed 'Crackling Jade Lightning', enabled by default. Can be turned on or off in the settings.",       
"PRIEST",
"Discipline: Improved offensive 'Penance' usage.",  

"SHAMAN",
"Elemental: Custom rotation: Fixed an issue with 'Ascendance' during single target situations.",
"Restoration: Fixed an issue with the single target rotation.",

"SHAMAN",
"Elemental and Enhancement: Changes to Mouseover Spells. Redo your autobind.",
"MONK",
"Windwalker and Mistweaver: Changes to Mouseover Spells. Redo your autobind.",
"PRIEST",
"Discipline: Added a 'Purge the Wicked x Penance' logic. Should highly improve the accuracy on 'Purge the Wicked' spread. Redo your autobind.",   
  
"MONK",
"Brewmaster: Fixed a pretty stupid typo that caused the rotation to try to force 'Tiger Palm' during the opener when playing 'Press the Advantage'.",
"SHAMAN",
"Restoration: Included 'Acid Rain' check in the DPS rotation.",  
"PRIEST",
"Discipline: Improved 'Purge the Wicked' handling if we are able to 'Penance' spread.",   
 
"SHAMAN",
"Elemental: Custom rotation: Changed Maelstrom pooling behaviour. We now only pool in AoE scenarios.",

"SHAMAN",
"Elemental: Fixed a LUA error when playing 'Echoes of Great Sundering'.",

"MONK",
"Mistweaver: Added a 'Stand still threshold' to 'Crackling Jade Lightning'.",

21.11.24      
"SHAMAN",
"Elemental: Added a setting to make use of a custom APL/rotation. Especially recommended if you play 'Echoes of Great Sundering'.",
"Elemental: Custom rotation: improved Malestrom pooling before 'Ascendance'."
"Elemental: Custom rotation: improved 'Earth Shock'/'Elemental Blast' x 'Earthquake' weaving."
"Elemental: Custom rotation: improved 'Lightning Rod' spread."
"Restoration: Added settings for 'Wellspring'."
"MONK",
"Mistweaver: Added 'Jade Empowerment' logic, will be handled full auto by the rotation.",

20.11.24      
"SHAMAN",
"Elemental: Updated AoE rotation.",

20.11.24
"SHAMAN",
"Enhancement: Custom rotation: improved syncing between 'Ascendance' and 'Doom Winds'.",
"Enhancement: Improved 'Tempest pooling' logic.",
"Enhancement: Improved Trinket logic for 'Skardnys Grace'.",
"Restoration: New 'Tidal Waves' logic. This is mostly handled full-auto, but you can customize parts of it. Check the new 'Tidal Waves (ST)' and 'Tidal Waves (AoE)' settings.",

18.11.24      
"MONK",
"Windwalker: Added a Setting to only use specific spells after a 'Stand still threshold'. Disabled by default.",
"Windwalker: Removed outdated settings.",

18.11.24      
"SHAMAN",
"Elemental: Added a setting to choose at how many targets we transition into the AoE rotation.",
"Restoration: Fixed an error in regards to 'Reactive Warding'.",

18.11.24      
"MONK",
"Mistweaver: Fixed a issue in regards to 'Faeline Stomp' resets.",

18.11.24      
"MAGE",
"Frost: Fixed a big whoopsie in regards to 'Cone of Cold' resets...",
"SHAMAN",
"Enhancement: Changes to the Custom rotation in regards to 'Doom Winds', 'Ascendance' and 'Windstrike' priorities.",
18.11.24      
"DEATHKNIGHT",
"Frost: Major APL update.",
"Frost: Updated Trinket handling.",
"Frost: Updated Rotation during Cooldown windows.",

18.11.24      
"MAGE",
"Frost: Fixed 'Freeze' behaviour.",
"Frost: Changed 'Cone of Cold' handling in regards to how and when we reset 'Comet Storm' and 'Frozen Orb'.",

18.11.24      
"MAGE",
"Frost: Fixed 'Fire Blast' usage during movement.",

17.11.24      
"MAGE",
"Frost: minor APL update.",

17.11.24      
"SHAMAN",
"Enhancement: Major update. And this time you dont need to reset your Class Database... yay!",
"Enhancement: Updated 'Save Maelstrom' toggle'.",
"Enhancement: Added a setting to save Maelstrom/Tempest for the next pull, disabled by default. Can be customized with a time-to-die.",
"Enhancement: Added a setting to make use of a custom APL/rotation. More information in the pinned message in the spec channel.",
"MONK",
"Brewmaster: Small change to the 'Ring of Peace' safety feature.",


15.11.24      
"SHAMAN",
"Elemental: Major rework. Please reset your Class Database. I swear this will be the last time...",
"Elemental: Updated Single Target rotation (Farseer).",
"Elemental: Updated Single Target rotation (Stormbringer).",
"Elemental: Updated AoE rotation (Farseer).",
"Elemental: Updated AoE rotation (Stormbringer).", 
"Elemental: Added a setting to only automate usage of Ghost Wolf outside of Dungeons and Raids.", 
"Enhancement: Added a setting to make sure we enter 'Ascendance' quicker in singletarget situations (disabled by default).", 
"Restoration: Adjusted pre-ramp timing."  
"MAGE",
"Frost: Updated AoE rotation (Spellslinger).",   
"Fire: Updated handling of specific trinkets during 'Combustion' (Sunfury and Frostfire).",
"MONK",
"Brewmaster: Updates to 'Expel Harm' when using 'Auto defensive CDs'.", 
"Mistweaver: Changed the priority some of the healing spells.",    
"Mistweaver: Adjusted pre-ramp timing."  
"DRUID",
"Restoration: Changed the priority some of the healing spells."
"Restoration: Minor changes to 'Grove Guardians'." 
"Restoration: Adjusted pre-ramp timing."  
"PRIEST",
"Discipline & Holy: Adjusted pre-ramp timing."  
"PALADIN",
"Protection: Updated 'Hammer of Light' logic."  
"Protection: Updated Standard rotation." 

11.11.24      
"SHAMAN",
"Enhancement & Restoration: Major rework (once again...). Please reset your Class Database.", 
"Enhancement & Restoration: Changed settings for 'Earth Elemental'.", 
"Enhancement: Updated Single Target rotation (Totemic).",
"Enhancement: Updated Single Target rotation (Stormbringer).",
"Enhancement: Updated AoE rotation (Totemic).",
"Enhancement: Updated AoE rotation (Stormbringer).",   
"Enhancement: Added a setting to only use Tempest on our main target.",    
"Restoration: Changed the priority on basically almost every healing spell.",  
"Restoration: Removed some unnecessary and outdated settings.",   
"Restoration: Added a setting to only automate usage of Ghost Wolf outside of Dungeons and Raids.",   
"Restoration: Changed logic on 'Surging Totem' in regards to 'Totemic Projection'.",   
"Restoration: Added a setting to choose on what Totems 'Totemic recall' is used on.",   

10.11.24      
"SHAMAN",
"Enhancement: Small changes to 'Lively Totems' handling.",    

08.11.24      
"WARLOCK",
"Demonology: Complete rework / major APL update! Please reset your Class database.",
"Demonology: Removed 'Demonbolt' opener option.",
"Demonology: Added a setting to sync Vilefiend and Tyrant usage.",
"Demonology: Removed 'Force sync Grimmoire: Felguard & Tyrant' setting.", 
"Demonology: Reworked Tyrant rotation.",
"Demonology: Reworked AoE rotation.",
"Demonology: Reworked ST rotation.",
"Demonology: Reworked Filler rotation.",
"Affliction: Minor APL update.",
"PRIEST",
"Discipline & Holy: New fully automatic Power Infusion logic. Thanks @human for making sense of my spaghetti code, fixing and implementing <3. Thx @Spitfirezz for helping with the list.",  
"MAGE",
"Frost: Reworked 'Ice Floes' logic and settings. Rotation will now use 'Ice Floes' according to your settings but will make sure to keep a charge for 'Ray of Forst' and/or 'Shifting Power' instead of wasting all three on 'Glacial Spike'.",

06.11.24      
"MAGE",
"Frost: Fixed an issue that caused Ice Lance to not be used during movement while at five Icicles.",
"SHAMAN",
"Restoration: Updated incoming damage logic (auto pre-ramp).",
"PRIEST",
"Discipline: Please reset your Class Database.", 
"Discipline: Updated incoming damage logic (auto pre-ramp).",  
"Discipline: Reworked logic for 'Premonition of Piety'.", 
"Discipline: Reworked logic for 'Premonition of Insight'.",   
"Discipline: Reworked logic for 'Premonition of Solace'.",     
"Discipline: Reworked logic for 'Premonition of Clairvoyance'.",
"Discipline: Reworked Ramp toggle. We now have a single toggle for all raid ramps and the m+ ramp. The correct one will be used automatically.",  
"Holy: Updated incoming damage logic (auto pre-ramp).",     
"MONK",
"Mistweaver: Updated incoming damage logic (auto pre-ramp).",    
"DRUID",
"Restoration: Updated incoming damage logic (auto pre-ramp).",    

04.11.24
"SHAMAN",
"Enhancement: Minor APL update.",
"MAGE",
"Frost: Minor APL update.",
"PRIEST",
"Shadow: Minor APL update.",

03.11.24
"MAGE",
"Frost: Minor APL update.",

03.11.24
"SHAMAN",
"Enhancement: Major APL update.",
"Enhancement: Updated Single Target rotation (Totemic).",
"Enhancement: Updated Single Target rotation (Stormbringer).",
"Enhancement: Updated AoE rotation (Totemic).",
"Enhancement: Updated AoE rotation (Stormbringer).",
"Enhancement: Added support for 'Voltaic Blaze'.",

03.11.24
"MONK",
"Windwalker: Rotation reworked!",
"Windwalker: Please reset your Class Database.",
"Windwalker: Introduced a new setting that handles the usage of 'Whirling Dragon Punch' when we are moving, enabled by default.",
"Windwalker: Introduced a setting to make use of a custom APL', disabled by default. I highly recommend enabling it tho. Especially for m+.",
"Windwalker: Introduced a new setting that handles the usage Vivify for self healing while we have Vivacious Vivification, enabled by default.",
"Windwalker: Changed 'Treacherous Transmitter' Trinket handling to make sure its used correctly when playing Conduit.",
"Windwalker: Updated AoE rotation.",
"Windwalker: Updated Single Target rotation.",
"Windwalker: Removed a setting called 'Tiger Palm - Range usage' since it causes issues and Tiger Palm's range is fixed now.
"Windwalker: Changed default settings.",

31.10.24
"PRIEST",
"Shadow: Rotation updated!.",
"Shadow: Removed Dragonflight Trinkets.",
"Shadow: Updated handling of 'Shadow Word: Death'.",
"Shadow: Introduced a new setting that assigns a custom, higher priority to 'Devouring Plague', enabled by default.",
"Shadow: Updated handling of 'Devouring Plague'.",
"Shadow: Updated handling of 'Void Torrent'.",
"Shadow: Updated handling of 'Vampiric Touch'.",
"Shadow: Updated AoE rotation.",
"Shadow: Updated Single Target rotation.",

30.10.24
"SHAMAN",
"Restoration: Added a Setting for the second charge of 'Healing Stream Totem'.",

30.10.24
"MAGE",
"Frost: Updated Spellslinger AoE rotation.",
"Frost: Updated Frostfire AoE rotation.",
"Frost: Updated Spellslinger Cleave rotation.",
"SHAMAN",
"Enhancement: Reworked rotation once again.",
"Enhancement: Added Funnel rotation and toggle back because its a thing again.",
"Enhancement: Updated Single Target rotation (Totemic).",
"Enhancement: Updated Single Target rotation (Stormbringer).",
"Enhancement: Updated AoE rotation (Totemic).",
"Enhancement: Updated AoE rotation (Stormbringer).",

28.10.24
"MAGE",
"Fire: Please reset your Class Database.",
"Fire: Honestly. Please reset it. There WILL be issues if you dont.",
"Fire: Added Rotations for each Hero Talent Tree (Frostfire and Sunfury).",
"Fire: Added 'Combustion' Rotations for each Hero Talent Tree (Frostfire and Sunfury).",
"Fire: Fixed 'Meteor' handling when no Combustion is up or ready.",
"Fire: Added 'Frostfire Empowerment' handling.",
"Fire: Added 'Heat Shimmer' handling.",
"Fire: Cleaned up settings, removed unnecessary settings.",
"Fire: Added some settings to handle 'Fire Blast' and 'Phoenix Flames' pooling before entering 'Combustion'.",
"Fire: Changed the way we handle pooling for 'Sun King's Blessing'.",
"Fire: Changed default Settings.",
"Fire: Changed target count at which we start to spend Hot Streaks on 'Flamestrike' instead of 'Pyroblast'.",
"Fire: Added a Setting to use 'Dragon's Breath' offensively (disabled by default).",
"Fire: Fixed an issue that caused our opener 'Fireball' to be cancelled.",

27.10.24
"DRUID",
"Balance: Added a Setting to only use CDs after a 'Stand still threshold'.",
"MAGE",
"Frost: Slight changes to the 'Stand still threshold' setting. (Please reset your Class Database)",

23.10.24
"MAGE",
"Frost: Non-Frostbolt ST APL changes.",
"DRUID",
"Balance: 'Convoke' ST Changes.",
"Balance: 'Wrath' / 'Starfire' ST Changes.",
"Balance: Added a Setting to only use Cooldowns when Enemies have DoTs up already.",

23.10.24
"MAGE",
"Frost: Fixed Cold Snap usage.",

23.10.24
"MONK",
"All Specs: Added new Talents, Buffs, SpellIDs to the Rotations.",
"All Specs: Added TWW Trinkets to Spelllist.",
"Brewmaster and Mistweaver: Updated to 11.0.5!",
"Brewmaster and Mistweaver: This will probably need to be revised further, but i have to wait until guides are updated :x",
"Mistweaver: Added 'Rushing Wind Kick' to the rotation.",
"MAGE",
"All Specs: Added new Talents, Buffs, SpellIDs to the Rotations.",
"Frost: Updated to 11.0.5!",
"Frost: Added 'Spymaster's Web' and 'Imperfect Ascendancy Serum' handling.",
"Frost: Revised Cleave Rotation (Frostfire).",
"Frost: Revised Cleave Rotation (Splinterstom).",
"Frost: Revised AoE Rotation (Frostfire).",
"Frost: Revised AoE Rotation (Splinterstom).",
"Frost: Revised ST Rotation (Frostfire).",
"Frost: Revised ST Rotation (Splinterstom).",
"Frost: Revised Trinket Usage.",
"Frost: Implemented APL changes.",
"Frost: Removed old Dragonflight related stuff.",
"SHAMAN",
"All Specs: Added new Talents, Buffs, SpellIDs to the Rotations.",
"Enhancement: Updated to 11.0.5!",
"Enhancement: This will probably need to be revised further, but i have to wait until guides are updated :x",
"Restoration: We will now automatically drop a Tremor Totem on the Ingra Maloch encounter in Tirna Scithe (m+ diffuculty).",
"DRUID",
"All Specs: Added new Talents, Buffs, SpellIDs to the Rotations.",
"Balance: Updated to 11.0.5!",
"Balance: Revised AoE Rotation.",
"Balance: Revised 'Force of Nature' usage.",

20.10.24
"MONK",
"Brewmaster: Revised rotation once again.",
"Brewmaster: Revised 'auto defensives'.",
"Brewmaster: Revised 'Master of Harmony' rotation.",
"Brewmaster: Changed the way 'Expel Harm' is handled.",
"Brewmaster: Added 'Ring of Peace on Danger' setting.",

------------------------------------

18.10.24
"DEATH KNIGHT",
"Frost: Major APL update.",
"Frost: Fixed miscalculation on resources during 'Breath of Sindragosa'.",

17.10.24
"MAGE",
"Frost: Minor update for entering AOE.",

17.10.24
"MAGE",
"Frost: Updated 'Spymaster's Web' Trinket conditions.",

"MONK",
"Mistweaver: Added 'Mana Tea auto' logic and setting.",
------------------------------------
14.10.24
"SHAMAN",
"Enhancement: Completly reworked. Custom rotation, not build on simc APL anymore.",
"Enhancement: Added logics for Stormbringer builds (both Elementalist and Storm, all Talents should be fully supported).",
"Enhancement: Added logics for Totemic builds (both Elementalist and Storm, all Talents should be fully supported).",
"Enhancement: Removed Funneling rotation (we don't do that anymore).",
"Enhancement: Changed default settings.",
"Enhancement: Added 'Earth Elemental DPS' setting.",
"Enhancement: Added a setting to only automate usage of Ghost Wolf outside of Dungeons and Raids.",
    
------------------------------------

13.10.24
"MONK",
"Mistweaver: Revised pre-ramp logic.",
"Mistweaver: Revised Chi-Ji logic and rotation.",
"Mistweaver: Removed 'Chi-Burst' from fillers.",
"Mistweaver: Added 'Chi-Burst x Jadefire reset' logic.",
"Mistweaver: Revised Unity Within logic.",
"Mistweaver: Revised Yu-Lon logic and rotation.",
"Mistweaver: Revised Yu-Lon ramp.",

------------------------------------

12.10.24
---SHAMAN RESTORATION---
revised pre-ramp logic
cleaned up settings
removed "Cloudburst/HealingStream anticap" setting
added "Cloudburst/HealingStream high priority" setting
Fixed Riptide usage in regards to tidal waves
added tidal waves logic
added overlap logic for Healing Stream Totem
added Earth Elemental on aggro
added "Undulation - Auto" setting
TODO added Wellspring

------------------------------------

04.10.24
---MAGE FIRE---
changed priority on taking aggro
made sure we dont invis on aggro while mirror images are still up
added setting to enable/disable DB fish at the end of combustion
added "Ice Cold" defensive usage



---MAGE FROST---
changed priority on taking aggro
made sure we dont invis on aggro while mirror images are still up
removed "Dragon's Breath offensive" setting (wasnt doing anything)
combined "Stand still tresholds" and included CS
slightly changed "Cone of Cold" conditions and optimized usage
optimized "Shifting Power" usage
slightly optimized "Blizzard" usage in regards to "Frozen Orb"
added "Glacial Spike" on AoE with the correct conditions
added "Ice Cold" defensive usage
added "Ice Block" defensive usage
added "Cold Snap" setting to reset defensives

"MONK",
"Brewmaster: Simplified opener code by consolidating both opener sequences into a single unified version.",

"MONK",
"Brewmaster: Fixed opener sequence to follow the correct priority order.",
"Brewmaster: Updated RushingJadeWind, KegSmash, BlackoutKick, TigerPalm, and BreathofFire sequence in opener.",

"MONK",
"Brewmaster: Simplified Black Ox Brew handling by integrating it directly into ST and AoE rotations.",
"Brewmaster: Removed bob_smart setting in favor of consistent Black Ox Brew logic - use when energy is low and Purifying Brew/Celestial Brew are on cooldown.",

"MONK",
"Brewmaster: Modified Celestial Brew logic to cast when either near max charges/stored vitality with Aspect of Harmony, or when below 90% health without the talent.",

"MONK",
"Brewmaster: Updated Niuzao dropdown to use descriptive keys instead of numeric values.",

"Brewmaster: Added Niuzao usage mode selection (Offensive/Defensive) to control when Invoke Niuzao is used.",
"Brewmaster: Renamed Niuzao stagger threshold setting for clarity.",

"Brewmaster: Added logic to use Breath of Fire to gain Charred Passions when the 11.1 Class Set 4pc bonus (LuckOfTheDraw) is active.",

